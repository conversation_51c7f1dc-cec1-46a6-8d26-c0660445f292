package com.jd.kf.oss.performance.domain.runtime.domain.performanceuser;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/07/04
 * 用户信息仓储接口
 */
public interface IPerformanceUserRepository {

    /**
     * 查询当前绩效月的所有用户数据
     * @param period 绩效月
     * @return 用户列表
     */
    List<PerformanceUserDO> queryAllUserByPeriodAndBusinessLineId(String businessLineId, String period);

    /**
     * 查询当前绩效月的所有用户数据
     * @param period 绩效月
     * @return 用户列表
     */
    List<PerformanceUserDO> queryUserByBusinessLineIdAndErps(String businessLineId, String period, List<String> erps);

    /**
     * 查询当前绩效月的所有用户数据
     * @param period 绩效月
     * @return 用户列表
     */
    List<PerformanceUserDO> queryUserByErps(String period, List<String> erps);

    /**
     * 查询当前绩效月的所有用户数据
     * @param period 绩效月
     * @return 用户列表
     */
    PerformanceUserDO queryUserByErp(String period, String erp);
}

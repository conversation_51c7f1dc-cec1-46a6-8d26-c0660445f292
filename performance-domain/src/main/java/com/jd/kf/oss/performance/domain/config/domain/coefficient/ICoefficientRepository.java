package com.jd.kf.oss.performance.domain.config.domain.coefficient;


import com.jd.kf.oss.performance.enums.CoefficientTemplateTypeEnum;
import com.jd.kf.oss.performance.utils.CommonPage;

import java.util.List;

public interface ICoefficientRepository {
    /**
     * 插入聚合DO系数配置
     * @param coefficientDO
     * @return
     */
    boolean insertCoefficientDO(CoefficientDO coefficientDO);

    /**
     * 查询聚合DO系数配置,根据系数编码和绩效月
     * @param coefficientCode
     * @param period
     * @return
     */
    CoefficientDO selectCoefficientDOByCodeAndPeriod(String  coefficientCode, String period);

    /**
     * 更新聚合DO系数配置
     * @param coefficientDO
     * @return
     */
    boolean updateCoefficientDO(CoefficientDO coefficientDO);

    /**
     * 删除聚合DO系数配置,根据系数编码和绩效月
     * @param coefficientCode
     * @param period
     * @return
     */
    boolean deleteCoefficientDOByCodeAndPeriod(String  coefficientCode, String period);

    /**
     * 分页查询聚合DO系数配置（已废弃，使用字段参数版本）
     * @param pageCommand
     * @return
     */
    // CommonPage<CoefficientDO> pageDO(PageCommand<CoefficientDO> pageCommand);


    /**
     * 根据名称、租户代码和期间查询系数
     * @param name 系数名称
     * @param tenantCode 租户代码
     * @param period 期间
     * @return 匹配的系数数据对象
     */
    CoefficientDO selectCoefficientByName(String name,String tenantCode,String period);

    /**
     * 根据系数名称和类型分页查询系数配置
     * @param name 系数名称，可选
     * @param period 绩效月，可选
     * @param type 系数类型，可选
     * @param pageNum 页码
     * @param pageSize 页面大小
     * @return 分页结果
     */
    CommonPage<CoefficientDO> queryCoefficientByNameAndType(String name, String period, CoefficientTemplateTypeEnum type,
                                                            int pageNum, int pageSize);

    /**
     * 根据绩效月查询所有聚合系数
     */
    List<CoefficientDO> selectAllCoefficientDOByPeriod(String period);

    /**
     * 根据指定绩效月统计系数DO的数量
     */
    int countCoefficientDOByPeriod(String period);

    /**
     * 批量插入聚合系数数据
     */
    boolean batchInsertCoefficientDO(List<CoefficientDO> coefficientDOList);

}

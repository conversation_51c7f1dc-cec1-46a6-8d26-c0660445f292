package com.jd.kf.oss.performance.domain.config.domain.index;

import com.jd.dal.common.utils.CollectionUtils;
import com.jd.kf.oss.performance.context.UserContextHolder;
import com.jd.kf.oss.performance.enums.IndexTemplate;
import com.jd.kf.oss.performance.exception.BizException;
import com.jd.kf.oss.performance.utils.CheckUtil;
import com.jd.kf.oss.performance.utils.CommonPage;
import com.jd.kf.oss.performance.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/06/24
 */
@Service
@Slf4j
public class IndexDomainService {
    @Resource
    private IIndexRepository indexRepository;

    /**
     * 分页查询绩效组基础信息（已废弃，使用字段参数版本）
     * @param pageCommand 分页查询命令对象，包含分页参数和查询条件
     * @return 包含分页结果的通用分页对象
     */
    /*
    public CommonPage<IndexDO> pageIndexBasic(PageCommand<IndexDO> pageCommand) {
        return indexRepository.pageIndexBasic(pageCommand);
    }
    */

    /**
     * 根据条件分页查询指标基础信息
     * @param tenantCode 租户标识
     * @param kpiCd 指标code，可选
     * @param kpiName 指标名称，可选
     * @param status 指标状态，可选
     * @param pageNum 页码
     * @param pageSize 页面大小
     * @return 包含分页结果的通用分页对象
     */
    public CommonPage<IndexDO> queryIndexByConditions(String tenantCode, String kpiCd, String kpiName, String status,
                                                      int pageNum, int pageSize) {
        return indexRepository.queryIndexByConditions(tenantCode, kpiCd, kpiName, status, pageNum, pageSize);
    }

    /**
     * 根据绩效组ID、租户编码和期间查询指标列表
     * @param businessLineId 绩效组ID
     * @param tenantCode 租户编码
     * @param period 期间
     * @return 指标数据对象列表
     */
    public List<IndexDO> queryIndexesByBusinessId(String businessLineId, String tenantCode, String period) {
        return indexRepository.queryIndexesByBusinessId(businessLineId, tenantCode, period);
    }

    /**
     * 根据业务线ID和绩效周期查询指标
     *
     * @param period
     * @return
     */

    public List<IndexDO> queryIndexDOsByPeriodAndBusinessLineId(List<String> businessLineIds, String period) {
        return indexRepository.queryIndexPOsByPeriodAndBusinessLineId(businessLineIds, period);
    }

    /**
     * 从原始数据源查询所有指标数据
     * @return 包含所有指标数据的列表
     */
    public List<IndexDO> queryAllIndexDOFromOrigin() {
        return indexRepository.queryAllIndexDOFromOrigin();
    }

    public boolean saveBatchIndexes(List<IndexDO> dos, String businessId, String period) {
        if (dos == null || dos.isEmpty()) {
            return true;
        }
        for (IndexDO indexDO : dos) {
            indexDO.setBusinessLineId(businessId);
            indexDO.setPeriod(period);
        }

        return indexRepository.saveBatchIndex(dos);
    }


    public boolean saveBatchIndex(List<IndexDO> dos) {
        if (dos == null || dos.isEmpty()) {
           return true;
        }
        return indexRepository.saveBatchIndex(dos);
    }

    public boolean deleteIndexesByLineIdAndPeriod(String businessLineId, String period) {
        return indexRepository.deleteIndexesByLineIdAndPeriod(businessLineId, period);
    }

    public boolean updateBatchIndexDOById(List<IndexDO> dos) {
        if (dos == null || dos.isEmpty()) {
            return true;
        }
        return indexRepository.updateBatchIndexDOById(dos);
    }

    /**
     * 同步平台指标列表与绩效基础指标列表数据
     * @param platformList 平台指标数据列表
     * @param basicList 绩效基础基础指标数据列表
     */
    public void syncIndexBasicWithPlatform(List<IndexDO> platformList, List<IndexDO> basicList) {
        indexRepository.syncIndexBasicWithPlatform(platformList, basicList);
    }


    /**
     * 校验更新绩效目标时的指标code是否存在
     */

    private void checkAllIndexCodesExists(List<String> kpiCds, List<IndexBasicDO> indexBasicInDB) {
        if (CollectionUtils.isEmpty(kpiCds)) {
            return;
        }
        if(CollectionUtils.isEmpty(indexBasicInDB)) {
            throw new BizException("当前系统不存在相应的指标code");
        }
        Set<String> indexCodesInDB = indexBasicInDB.stream()
                .map(IndexBasicDO::getKpiCd)
                .collect(Collectors.toSet());
        List<String> notExistCodes = kpiCds.stream()
                .filter(code -> !indexCodesInDB.contains(code))
                .collect(Collectors.toList());
        // 如果notExistCodes不为空，说明有指标code在系统中不存在
        if (CollectionUtils.isNotEmpty(notExistCodes)) {
            log.error("指标code不一致,用户提供的指标code: {}，系统存在的指标code: {}", kpiCds, indexCodesInDB);
            throw new IllegalArgumentException("存在指标code与系统中现有的指标code不一致");
        }

    }

    /**
     * 校验指标是否一致
     * @param indexDO
     * @param indexDOInDB
     */
    public  void checkIndexSame(IndexDO indexDO, IndexBasicDO indexDOInDB) {
        if (indexDO == null && indexDOInDB == null) {
            return;
        }
        if (indexDO == null || indexDOInDB == null) {
            throw new IllegalArgumentException("指标不一致");
        }
        CheckUtil.equals(indexDO.getKpiName(), indexDOInDB.getKpiName(), "指标名不一致");
        CheckUtil.equals(indexDO.getKpiCd(),indexDOInDB.getKpiCd(),"指标code不一致");
    }

    /**
     * 查询所有指标基础信息列表
     * @return 包含所有指标基础信息的列表，列表元素类型为IndexDO
     */
    public List<IndexDO> queryAllIndexBasic() {
        return indexRepository.queryAllIndexBasic();
    }

    /**
     * 查询所有指标基础信息列表
     * @return 包含所有指标基础信息的列表，列表元素类型为IndexDO
     */
    public List<IndexDO> queryAllIndexValidBasic() {
        return indexRepository.queryAllValidIndexBasic();
    }

    /**
     * 根据指标名称列表查询指标基础信息
     * @param kpiNames 指标名称列表，不能为空
     * @return 指标基础信息DO对象列表
     */
    public List<IndexBasicDO> queryIndexBasicByKpiNames(List<String> kpiNames) {
        if (CollectionUtils.isEmpty(kpiNames) ) {
            throw new IllegalArgumentException("查询指标基础信息失败，指标编码或者绩效月不能为空");
        }
        return indexRepository.selectIndexBasicByKpiNames(kpiNames);
    }

    /**
     * 根据指标编码列表查询指标基础信息
     * @param kpiCds 指标编码列表，不能为空
     * @return 指标基础信息DO对象列表
     */
    public List<IndexBasicDO> queryIndexBasicByKpiCds(List<String> kpiCds) {
        if(CollectionUtils.isEmpty(kpiCds) ) {
            throw new IllegalArgumentException("查询指标基础信息失败，指标编码以及绩效月不能为空");
        }
        return indexRepository.selectIndexBasicByKpiCds(kpiCds);
    }


    /**
     * 只有方案使用到了指标才会进行更新
     * 为了兜底方案更新后，可能存在冗余或者缺失的指标模板，需要兜底diff
     * @param indexesOfLineInDB
     * @param importIndexes
     */

    @Transactional(rollbackFor = Exception.class)
    public void diffUpdateByImportIndexes(Map<String,List<IndexDO>> indexesOfLineInDB, Map<String, List<IndexDO>> importIndexes) {
        if(CollectionUtils.isEmpty(importIndexes)) {
            return;
        }
        List<IndexDO> indexesToInsert = new ArrayList<>();
        List<IndexDO> indexesToUpdate = new ArrayList<>();
        List<Long> indexesToDelete=new ArrayList<>();
        for(Map.Entry<String,List<IndexDO>> entry : importIndexes.entrySet()) {
            String businessLineId = entry.getKey();
            List<IndexDO> importIndexList = entry.getValue();
            List<IndexDO> indexesInDB = indexesOfLineInDB.getOrDefault(businessLineId, Collections.emptyList());
            Map<IndexTemplate, IndexDO> indexInDBMap =indexesInDB.stream().collect(Collectors.toMap(IndexDO::getTemplate,
                   Function.identity(),
                   (e, r) -> e));
            indexesToInsert.addAll(getIndexesToInsert(importIndexList, indexInDBMap));
            indexesToUpdate.addAll(getIndexesToUpdate(importIndexList, indexInDBMap));
            indexesToDelete.addAll(getIndexesToDelete(importIndexList,indexInDBMap));
        }
        indexRepository.deleteBatchById(indexesToDelete);
        indexRepository.saveBatchIndex(indexesToInsert);
        indexRepository.updateBatchIndexDOById(indexesToUpdate);
    }

    private List<IndexDO>  getIndexesToInsert(List<IndexDO> importIndexes,Map<IndexTemplate, IndexDO> indexInDBMap) {
        return importIndexes.stream().filter(indexDO -> !indexInDBMap.containsKey(indexDO.getTemplate()))
                .collect(Collectors.toList());
    }

    private List<IndexDO> getIndexesToUpdate(List<IndexDO> importIndexes, Map<IndexTemplate, IndexDO> indexInDBMap) {
        List<IndexDO> indexesToUpdate = new ArrayList<>();
        for(IndexDO importIndex : importIndexes) {
            IndexDO indexInDB = indexInDBMap.get(importIndex.getTemplate());
            if (indexInDB != null) {
                indexInDB.setThreshold(Optional.ofNullable(importIndex.getThreshold()).orElse(""));
                indexInDB.setWeight(Optional.ofNullable(importIndex.getWeight()).orElse(""));
                indexInDB.setStartDate(Optional.ofNullable(importIndex.getStartDate()).orElse(""));
                indexInDB.setEndDate(Optional.ofNullable(importIndex.getEndDate()).orElse(""));
                // 这里是将导入的指标信息填充到数据库中的指标信息中
                updateIndexByImportIndex(indexInDB, importIndex);
                indexesToUpdate.add(indexInDB);
            }
        }
        return indexesToUpdate;
    }

    private List<Long> getIndexesToDelete(List<IndexDO> importIndexes, Map<IndexTemplate, IndexDO> indexInDBMap) {
        Set<IndexTemplate> importTemplates = importIndexes.stream()
                .map(IndexDO::getTemplate)
                .collect(Collectors.toSet());

        return indexInDBMap.entrySet().stream()
                .filter(entry -> !importTemplates.contains(entry.getKey()))
                .map(entry-> entry.getValue().getId())
                .collect(Collectors.toList());
    }


    /**
     * 校验导入的指标模板和绩效方案中的指标模板是否一致
     */
    public void extractIndexByTemplateNameOfPlan(List<IndexDO> indexDOs, Set<String> planIndexTemplateCodes) {
        List<IndexDO> extractIndexes = new ArrayList<>();
        for (IndexDO indexDO : indexDOs) {
            if (planIndexTemplateCodes.contains(indexDO.getTemplate().getCode())) {
                extractIndexes.add(indexDO);
            }
        }
        if(extractIndexes.size()!=planIndexTemplateCodes.size()){
            throw new IllegalArgumentException("导入的指标模板和绩效方案中的指标模板数量不一致");
        }
        indexDOs.clear();
        indexDOs.addAll(extractIndexes);
    }


    /**
     * 给导入的指标校验指标编码是否存在
     * @param indexDOs
     * @param kpiNameAndIndexBasicMap
     */
    public void checkKpiNameExists(List<IndexDO> indexDOs, Map<String, IndexDO> kpiNameAndIndexBasicMap) {
        if (CollectionUtils.isEmpty(indexDOs)) {
            return;
        }
        if (kpiNameAndIndexBasicMap == null) {
            throw new IllegalArgumentException("绩效方案中的指标模板不存在");
        }
        StringBuilder stringBuilder = new StringBuilder();
        for (IndexDO indexDO : indexDOs) {
            String kpiName = indexDO.getKpiName();
            if (StringUtils.isBlank(kpiName)) {
               continue;
            }
            IndexDO indexBasicDO = kpiNameAndIndexBasicMap.get(kpiName);
            if (indexBasicDO == null) {
                if(stringBuilder.length()>0){
                    stringBuilder.append(";");
                }
                stringBuilder.append("指标名").append(kpiName).append("不存在");
            }
        }
        if(stringBuilder.length()>0){
             throw new IllegalArgumentException(stringBuilder.toString());
         }
    }

    public void populateImportIndexByIndexBasic(List<IndexDO> indexDOs,  Map<String, IndexDO> kpiNameAndIndexBasicMap) {
        if (CollectionUtils.isEmpty(indexDOs)) {
             return;
         }
        String period = DateUtils.getCurrentPerformancePeriod();
        for (IndexDO indexDO : indexDOs) {
            String kpiName = indexDO.getKpiName();
            //兜底操作,没有创建指标，但是关联了方案，补全绩效月
            indexDO.setPeriod(period);
            if(StringUtils.isNotBlank(kpiName)){
                IndexDO indexBasicDO = kpiNameAndIndexBasicMap.get(kpiName);
                //兜底操作，如果关联了方案，但是没有创建指标，插入的指标需要补全index的必要的信息
                populateImportIndexByIndexBasic(indexDO, indexBasicDO);
            }

        }
    }

    private void populateImportIndexByIndexBasic( IndexDO indexDO, IndexDO indexBasicDO) {
        indexDO.setKpiCd(indexBasicDO.getKpiCd());
        indexDO.setKpiName(indexBasicDO.getKpiName());
        indexDO.setDescription(indexBasicDO.getDescription());
        indexDO.setIndexPlatformUrl(indexBasicDO.getIndexPlatformUrl());
        indexDO.setIndexPlatformUrlCode(indexBasicDO.getIndexPlatformUrlCode());
    }

    private void updateIndexByImportIndex(IndexDO indexDO, IndexDO importIndexes) {
        indexDO.setKpiCd(Optional.ofNullable(importIndexes.getKpiCd()).orElse(""));
        indexDO.setKpiName(Optional.ofNullable(importIndexes.getKpiName()).orElse(""));
        indexDO.setDescription(Optional.ofNullable(importIndexes.getDescription()).orElse(""));
        indexDO.setIndexPlatformUrl(Optional.ofNullable(importIndexes.getIndexPlatformUrl()).orElse(""));
        indexDO.setIndexPlatformUrlCode(Optional.ofNullable(importIndexes.getIndexPlatformUrlCode()).orElse(""));
        indexDO.setModified(LocalDateTime.now());
        indexDO.setEditor(UserContextHolder.getOperator());
    }


    /**
     * 批量校验指标配置权重 下限 考核周期
     * @param indexDOs
     */
    public void checkIndexPropertiesConfig(List<IndexDO> indexDOs) {
        if(CollectionUtils.isEmpty(indexDOs)){
            return;
        }
        for(IndexDO indexDO:indexDOs) {
            indexDO.checkConfigs();
        }
    }

    /**
     * 校验全部的indexes
     * @param indexes
     */
    public  void checkAndPopulateIndexesConfig(List<IndexDO> indexes){
        if (CollectionUtils.isEmpty(indexes)) {
            return;
        }
        // 校验权重 样本下限 考核周期
        checkIndexPropertiesConfig(indexes);
        List<String> kpiCds = indexes.stream().map(IndexDO::getKpiCd)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(kpiCds)){
            return;
        }
        List<IndexBasicDO> indexBasicDOS = queryIndexBasicByKpiCds(kpiCds);
        // 校验指标code是否存在
        checkAllIndexCodesExists(kpiCds, indexBasicDOS);
        // 填充indexBasicDO的信息到indexDO中
        populateIndexByBasicIndexes(indexes, indexBasicDOS);
    }

    private void populateIndexByBasicIndexes(List<IndexDO> indexes,List<IndexBasicDO> indexBasicDOS){
        Map<String, IndexBasicDO> indexBasicMap = indexBasicDOS.stream()
                .collect(Collectors.toMap(IndexBasicDO::getKpiCd, indexBasicDO -> indexBasicDO,(e,r)->e));
        for (IndexDO indexDO : indexes) {
            if (StringUtils.isNotBlank(indexDO.getKpiCd())) {
                IndexBasicDO indexBasicDO = indexBasicMap.get(indexDO.getKpiCd());
                populateIndexByBasicIndexes(indexDO, indexBasicDO);
            }
        }
    }

    private void populateIndexByBasicIndexes(IndexDO indexDO, IndexBasicDO indexBasicDO) {
        indexDO.setKpiCd(indexBasicDO.getKpiCd());
        indexDO.setKpiName(indexBasicDO.getKpiName());
        indexDO.setIndexPlatformUrl(indexBasicDO.getIndexPlatformUrl());
        indexDO.setIndexPlatformUrlCode(indexBasicDO.getIndexPlatformUrlCode());
        indexDO.setDescription(indexBasicDO.getDescription());
    }

    /**
     *  批量删除绩效目标关联的index
     */
    public boolean deleteBatchByBusinessLineIdsAndPeriod(List<String> businessLineIds, String period){
        if (CollectionUtils.isEmpty(businessLineIds) ) {
           return true;
        }
        if(StringUtils.isBlank(period)) {
            throw new IllegalArgumentException("批量删除绩效目标关联的指标失败，绩效月不能为空");
        }
        return indexRepository.deleteBatchByBusinessLineIdsAndPeriod(businessLineIds, period);
    }

    /**
     * 以绩效方案解析的指标为模板，批量创建绩效组对应的指标
     * @param templateCodes
     * @param businessLineIds
     * @param period
     */
    public void associatePlanIndexesToBusinessLines(Set<String> templateCodes, List<String> businessLineIds, String period) {
       if(templateCodes == null || templateCodes.isEmpty()) {
           return;
        }
       if(CollectionUtils.isEmpty(businessLineIds) || StringUtils.isBlank(period)) {
           throw new IllegalArgumentException("创建绩效组对应的指标失败，绩效组ID和绩效月不能为空");
       }
        // 创建绩效组对应的指标 businessLineId  period
        List<IndexDO>  toInsert=new ArrayList<>(templateCodes.size()*businessLineIds.size());
        for (String businessLineId : businessLineIds) {
            for (String templateCode : templateCodes) {
                // 将指标模板名和条线、绩效月关联起来
                IndexDO indexDO = IndexDO.copyFromPlanIndex(templateCode, period, businessLineId);
                toInsert.add(indexDO);
            }
        }
        saveBatchIndex(toInsert);
    }

}

package com.jd.kf.oss.performance.domain.config.domain.factor;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jd.dal.common.utils.CollectionUtils;
import com.jd.kf.oss.performance.domain.config.domain.formula.FormulaNode;
import com.jd.kf.oss.performance.domain.config.domain.formula.FormulaParameter;
import com.jd.kf.oss.performance.domain.config.domain.formula.FormulaParser;
import com.jd.kf.oss.performance.utils.CheckUtil;
import com.jd.kf.oss.performance.utils.CommonPage;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class FactorDomainService {
    private static final ScriptEngineManager manager = new ScriptEngineManager();
    private static final ScriptEngine engine = manager.getEngineByName("JavaScript");
    @Resource
    private IFactorRepository factorRepository;

    /**
     * 加载所有因子
     * @param tenantCode
     * @param period
     * @return
     */
    public List<FactorDO> loadAllFactors(String tenantCode, String period) {
        return factorRepository.queryAll(tenantCode, period);
    }

    /**
     * 查询因子
     * @param tenantCode
     * @param period
     * @param factorName
     * @param type
     * @param pageNum
     * @param pageSize
     * @return
     */
    public CommonPage<FactorDO> queryFactors(String tenantCode, String period, String factorName, String type, Integer pageNum, Integer pageSize) {
        return factorRepository.queryByName(tenantCode, period, factorName, type, pageNum, pageSize);
    }

    /**
     * 判断是否成环
     * @param tenantCode
     * @param period
     * @param factorCodes
     * @return
     */
    public boolean checkCircle(String tenantCode, String period, List<String> factorCodes) throws Exception {
        if (CollectionUtils.isEmpty(factorCodes)) {
            return false;
        }
        List<FactorDO> factorDOS = loadAllFactors(tenantCode, period);
        if (CollectionUtils.isEmpty(factorDOS)) {
            return false;
        }
        Map<String, FactorDO> factorDOMap = factorDOS.stream().collect(Collectors.toMap(FactorDO::getCode, FactorDO->FactorDO));
        Map<String, Set<FactorDO>> graph = Maps.newHashMap();
        for (String factorCode : factorCodes) {
            buildCircle(factorDOMap.get(factorCode), graph, factorDOMap);
        }

        return hasCycle(graph);
    }

    /**
     * 判断是否成环
     * @param factorDO
     * @return
     */
    public boolean checkCircle(FactorDO factorDO) throws Exception {
        if (Objects.isNull(factorDO) || StringUtils.isBlank(factorDO.getFormula())) {
            return false;
        }
        List<FactorDO> factorDOS = loadAllFactors(factorDO.getTenantCode(), factorDO.getPeriod());
        if (CollectionUtils.isEmpty(factorDOS)) {
            return false;
        }
        Map<String, FactorDO> factorDOMap = factorDOS.stream().collect(Collectors.toMap(FactorDO::getCode, FactorDO->FactorDO));
        Map<String, Set<FactorDO>> graph = Maps.newHashMap();
        buildCircle(factorDO, graph, factorDOMap);
        return hasCycle(graph);
    }

    /**
     * 构建是否成环
     * @param factorDO
     * @param graph
     * @param factorDOMap
     */
    public void buildCircle(FactorDO factorDO, Map<String, Set<FactorDO>> graph, Map<String, FactorDO> factorDOMap) throws Exception{
        if (graph.get(factorDO.getCode()) != null) {
            return;
        }
        checkFormulaLegal(factorDO.getFormula());
        FormulaParser parser = new FormulaParser();
        try {
            FormulaNode ast = parser.parse(factorDO.getFormula());
            List<FormulaParameter> parameters = ast.getAllFactor();
            if (Objects.nonNull(ast) || CollectionUtils.isNotEmpty(parameters)) {
                Set<FactorDO> child = graph.getOrDefault(factorDO.getCode(), Sets.newHashSet());
                for (FormulaParameter parameter : parameters) {
                    FactorDO factorDO1 = factorDOMap.get(parameter.getName());
                    child.add(factorDO1);
                }
                graph.put(factorDO.getCode(), child);
            }
        } catch (Exception e) {
            throw new Exception("公式解析失败" + factorDO.getFormula());
        }
    }

    private boolean checkFormulaLegal(String formula) throws Exception {
        try {
            FormulaParser parser = new FormulaParser();
            FormulaNode ast = parser.parse(formula);
            if (Objects.isNull(ast)) {
                throw new Exception("公式解析失败" + formula);
            }
            String expression = String.valueOf(formula);
            for (FormulaParameter param : ast.getParams()) {
                expression = expression.replace(param.getOriginText(), "1");
            }
            for (FormulaNode param : ast.getFunctions()) {
                expression = expression.replace(param.getOriginText(), "1");
            }
            engine.eval(expression);
        } catch (Exception e) {
            throw new Exception("公式解析失败" + formula);
        }
        return false;
    }

    /**
     * 解析公式中的因子
     */
    public static Set<String> parseFormulaFactor(String formula, List<FactorDO> factorDOS){
        return parseFormula(formula, factorDOS, FormulaNode::getAllFactor);
    }

    /**
     * 解析公式中的指标和系数
     */
    public static Set<String> parseFormula(String formula, List<FactorDO> factorDOS,
                                           Function<FormulaNode,List<FormulaParameter>> dataExtractFunc){
        Set<String> dataCodes = new HashSet<>();
        FormulaNode ast = null;
        try {
            FormulaParser parser = new FormulaParser();
            ast = parser.parse(formula);
        } catch (Exception e) {
            // 处理解析异常
            return dataCodes;
        }
        if (ast != null) {
            List<FormulaParameter> coefficientParameters = dataExtractFunc.apply(ast);
            if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(coefficientParameters)) {
                dataCodes.addAll(coefficientParameters.stream()
                        .map(FormulaParameter::getName)
                        .collect(Collectors.toSet()));
            }
            if (ast.getAllFactor() != null) {
                List<FormulaParameter> factorParameters = ast.getAllFactor();
                if (factorParameters != null) {
                    List<FactorDO>  factorsOfCurrentFactorParameters = matchFactor(factorParameters, factorDOS);
                    for(FactorDO factorDO : factorsOfCurrentFactorParameters) {
                        if (com.jd.wormhole.util.StringUtils.isNotBlank(factorDO.getFormula())) {
                            dataCodes.addAll(parseFormula(factorDO.getFormula(), factorDOS, dataExtractFunc));
                        }
                    }
                }
            }
        }
        return dataCodes;
    }


    private static List<FactorDO> matchFactor(List<FormulaParameter> parameters, List<FactorDO> factorDOS) {
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(parameters)) {
            return new ArrayList<>(0);
        }
        Set<String> sets = parameters.stream().map(FormulaParameter::getName).collect(Collectors.toSet());
        return factorDOS.stream().filter(a -> sets.contains(a.getCode())).collect(Collectors.toList());
    }

    private boolean hasCycle(Map<String, Set<FactorDO>> graph) {
        Set<String> visited = new HashSet<>();
        Set<String> recursionStack = new HashSet<>();

        for (String node : graph.keySet()) {
            if (detectCycle(node, graph, visited, recursionStack)) {
                return true;
            }
        }
        return false;
    }
    private boolean detectCycle(String node,
                                Map<String, Set<FactorDO>> graph,
                                Set<String> visited,
                                Set<String> recursionStack) {
        if (recursionStack.contains(node)) return true;
        if (visited.contains(node)) return false;

        visited.add(node);
        recursionStack.add(node);

        for (FactorDO child : graph.getOrDefault(node, Collections.emptySet())) {
            if (detectCycle(child.getCode(), graph, visited, recursionStack)) {
                return true;
            }
        }
        recursionStack.remove(node);
        return false;
    }


    /**
     * 查询所有因子数据用于同步
     */
    public List<FactorDO> queryAllFactorDOByPeriod(String period) {
        CheckUtil.notBlank(period, "绩效月不能为空");
        return factorRepository.selectAllFactorDOByPeriod(period);
    }

    public int countAllFactorDOByPeriod(String period) {
        CheckUtil.notBlank(period, "绩效月不能为空");
        return factorRepository.countAllFactorDOByPeriod(period);
    }

    /**
     * 批量修改因子数据对象的元信息用于同步
     * @param factorDOS 需要修改的因子数据对象列表
     * @return 修改后的因子数据对象列表
     */
    public List<FactorDO> modifyFactorDOMetaInfoForSync(List<FactorDO> factorDOS,String targetPeriod) {
        if (CollectionUtils.isEmpty(factorDOS)) {
            return new ArrayList<>(0);
        }
        CheckUtil.notBlank(targetPeriod, "绩效月不能为空");
        LocalDateTime now = LocalDateTime.now();
        for(FactorDO factorDO : factorDOS) {
            modifyFactorDOMetaInfoForSync(factorDO, targetPeriod, now);
        }
        return factorDOS;
    }

    /**
     * 更新因子DO对象的元数据信息用于同步
     */
    public FactorDO modifyFactorDOMetaInfoForSync(FactorDO factorDO, String period, LocalDateTime createdAt) {
        if (factorDO == null) {
            return null;
        }
        factorDO.setId(null);
        factorDO.setPeriod(period);
        factorDO.setCreated(createdAt);
        factorDO.setModified(createdAt);
        return factorDO;
    }

    /**
     * 批量保存因子数据对象列表并限制每批次大小
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSaveFactorDOWithLimit(List<FactorDO> factorDOS, int batchSize) {
        if (CollectionUtils.isEmpty(factorDOS)) {
            return true;
        }
        CheckUtil.isTrue(batchSize > 0, "批量大小必须大于0");
        Lists.partition(factorDOS, batchSize).forEach(dos -> {
            factorRepository.batchInsertFactorDO(dos);
        });
        return true;
    }

}

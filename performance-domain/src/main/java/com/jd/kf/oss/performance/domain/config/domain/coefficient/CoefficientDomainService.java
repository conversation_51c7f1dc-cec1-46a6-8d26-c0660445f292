package com.jd.kf.oss.performance.domain.config.domain.coefficient;

import com.google.common.collect.Lists;
import com.jd.dal.common.utils.CollectionUtils;
import com.jd.kf.oss.performance.context.UserContextHolder;
import com.jd.kf.oss.performance.domain.config.domain.coefficient.utils.check.AbsCoefficientItemsCheck;
import com.jd.kf.oss.performance.enums.CoefficientTemplateTypeEnum;
import com.jd.kf.oss.performance.utils.CheckUtil;
import com.jd.kf.oss.performance.utils.CommonPage;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 绩效系数
 */
@Service
public class CoefficientDomainService {

    @Resource
    private ICoefficientRepository coefficientRepository;

    /**
     * 根据code和绩效月查询聚合DO
     * @param coefficientCode
     * @param period
     * @return
     */
    public CoefficientDO queryCoefficientDOByCodeAndPeriod(String coefficientCode, String period) {
        CheckUtil.notBlank(coefficientCode,period,"系数编码和周期不能为空");
        return coefficientRepository.selectCoefficientDOByCodeAndPeriod(coefficientCode, period);
    }

    /**
     * 更新聚合对象DO
     * @param coefficientDO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCoefficientDO(CoefficientDO coefficientDO) {
        CheckUtil.notNull(coefficientDO,"系数配置不能为空");
        CheckUtil.notBlank(coefficientDO.getCode(),coefficientDO.getPeriod(),"系数编码和周期不能为空");
        CoefficientDO coefficientDOInDB = coefficientRepository.selectCoefficientDOByCodeAndPeriod(coefficientDO.getCode(),
                coefficientDO.getPeriod());
        trimAndCheckCoefficientName(coefficientDO,coefficientDOInDB.getName());
        CheckUtil.notNull(coefficientDOInDB, "系数配置不存在，无法更新");
        checkCoefficientItems(coefficientDO,coefficientDOInDB.getType());
        coefficientDO.propagateCoefficientItem();
        return coefficientRepository.updateCoefficientDO(coefficientDO);
    }

    /**
     * 检查系数名称是否合法且未被占用
     * @param coefficientDO 包含待检查名称的系数数据对象
     * @param nameInDB 数据库中已存在的名称
     */
    private void trimAndCheckCoefficientName(CoefficientDO coefficientDO, String nameInDB) {
        // 去除name的两边空白
        coefficientDO.setName(coefficientDO.getName().trim());
        if(Objects.equals(coefficientDO.getName(), nameInDB)){
            return;
        }
        checkCoefficientNameNotExits(coefficientDO.getName(), coefficientDO.getPeriod());
    }


    /**
     * 根据系数模板类型检查系数项的有效性
     * @param coefficientDO 系数数据对象，包含需要检查的系数项
     * @param type 系数模板类型枚举，用于确定检查策略
     */
    private void checkCoefficientItems(CoefficientDO coefficientDO, CoefficientTemplateTypeEnum type){
        AbsCoefficientItemsCheck checkStrategy = AbsCoefficientItemsCheck.getCheckStrategy(type);
        checkStrategy.check(coefficientDO);
    }


    /**
     * 需要在聚合服务中判断是否被其他因子或者方案引用
     * 删除聚合DO系数配置,根据系数编码和绩效月
     * @param coefficientCode
     * @param period
     * @return
     */
    public boolean deleteCoefficientDOByCodeAndPeriod(String coefficientCode, String period) {
        CheckUtil.notBlank(coefficientCode,period,"系数编码和周期不能为空");
        return coefficientRepository.deleteCoefficientDOByCodeAndPeriod(coefficientCode, period);
    }

    /**
     * 创建聚合系数DO
     * @param coefficientDO
     * @return
     */
    public boolean saveCoefficientDO(CoefficientDO coefficientDO) {
        CheckUtil.notNull(coefficientDO, "系数配置不能为空");
        trimAndCheckCoefficientName(coefficientDO);
        coefficientDO.buildCode();
        coefficientDO.propagateCoefficientItem();
        coefficientDO.checkItemsByTemplateType();
        return coefficientRepository.insertCoefficientDO(coefficientDO);
    }
    /**
     * 对系数名称进行去除两边空白并检查是否已存在
     */
    private void trimAndCheckCoefficientName(CoefficientDO coefficientDO){
        // 去除name的两边空白 系数名不能为空
        String coefficientName =coefficientDO.getName().trim();
        coefficientDO.setName(coefficientName);
        checkCoefficientNameNotExits(coefficientName, coefficientDO.getPeriod());
    }
    /**
     * 校验系数名称在指定租户和周期下是否已存在，若存在则抛出异常
     * @param name 要校验的系数名称
     * @param period 要校验的周期
     */
    private void checkCoefficientNameNotExits(String name, String period) {
        // 校验系数项
        String tenantCode = UserContextHolder.getTenantCode();
        CoefficientDO coefficientDO = coefficientRepository.selectCoefficientByName(name, tenantCode, period);
        CheckUtil.isTrue(coefficientDO==null, "系数名称已存在，请修改后重试");
    }

    /**
     * 分页DO
     * 分页DO（已废弃，使用字段参数版本）
     * @param pageCommand
     * @return
     */
    /*
    public CommonPage<CoefficientDO> pageDO(PageCommand<CoefficientDO> pageCommand) {
        return coefficientRepository.pageDO(pageCommand);
    }
    */

    /**
     * 根据系数名称和类型分页查询系数配置
     * @param name 系数名称，可选
     * @param period 绩效月，可选
     * @param type 系数类型，可选
     * @param pageNum 页码
     * @param pageSize 页面大小
     * @return 分页结果
     */
    public CommonPage<CoefficientDO> queryCoefficientByNameAndType(String name, String period, CoefficientTemplateTypeEnum type,
                                                                   int pageNum, int pageSize) {
        // 保持与原有pageDO方法完全一致的校验逻辑
        // 原有方法通过PageCommand进行校验，这里直接进行相同的校验
        CheckUtil.notNull(pageNum, pageSize, "分页查询参数不能为空");
        CheckUtil.isTrue(pageNum > 0, "页码必须大于0");
        CheckUtil.isTrue(pageSize > 0, "页面大小必须大于0");
        // 直接使用字段参数调用Repository，参数顺序与Repository完全一致
        CommonPage<CoefficientDO> pageInfo = coefficientRepository.queryCoefficientByNameAndType(name, period, type, pageNum, pageSize);
        return pageInfo;
    }

    /**
     * 根据绩效月查询所有聚合系数
     */
    public List<CoefficientDO> queryAllCoefficientDOByPeriod(String period) {
        CheckUtil.notBlank(period, "绩效月不能为空");
        return coefficientRepository.selectAllCoefficientDOByPeriod(period);
    }

    /**
     * 根据指定绩效月统计系数DO的数量
     * @param period 绩效月(格式要求: YYYY-MM, 不能为空)
     */
    public int countCoefficientDOByPeriod(String period) {
        CheckUtil.notBlank(period, "绩效月不能为空");
        return coefficientRepository.countCoefficientDOByPeriod(period);
    }

    /**
     * 批量保存聚合系数
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSaveCoefficientDO(List<CoefficientDO> coefficientDOList) {
        if(CollectionUtils.isEmpty(coefficientDOList)){
            return true;
        }
        return coefficientRepository.batchInsertCoefficientDO(coefficientDOList);
    }


    /**
     * 批量保存系数数据对象列表，按照指定批次大小分批处理
     * @param coefficientDOList 需要保存的系数数据对象列表
     * @param batchSize 每个批次处理的数据量大小
     * @return 总是返回true，表示处理完成
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSaveCoefficientDOWithLimit(List<CoefficientDO> coefficientDOList,int batchSize) {
        if(CollectionUtils.isEmpty(coefficientDOList)){
            return true;
        }
        CheckUtil.isTrue(batchSize>0,"批次大小必须大于0");
        Lists.partition(coefficientDOList, batchSize).forEach(dos -> {
          coefficientRepository.batchInsertCoefficientDO(dos);
        });
        return true;
    }

    /**
     * 更新系数对象的元信息用于同步（包括周期和创建时间，并同步更新其关联项）
     */
    public List<CoefficientDO> modifyCoefficientDOMetaInfoForSync(List<CoefficientDO> coefficientDOList,String period) {
        if(CollectionUtils.isEmpty(coefficientDOList)){
            return new ArrayList<>(0);
        }
        CheckUtil.notBlank(period, "绩效月不能为空");
        LocalDateTime now = LocalDateTime.now();
        for(CoefficientDO coefficientDO : coefficientDOList) {
           modifyCoefficientDOMetaInfoForSync(coefficientDO, period, now);
        }
        return coefficientDOList;
    }
    
    /**
     * 更新系数对象的元信息用于同步（包括周期和创建时间，并同步更新其关联项）
     */
    public CoefficientDO modifyCoefficientDOMetaInfoForSync(CoefficientDO coefficientDO, String period, LocalDateTime createdAt) {
        if (coefficientDO == null) {
            return null;
        }
        modifyCoefficientMetaInfo(coefficientDO, period, createdAt);
        List<CoefficientItem> items = coefficientDO.getCoefficientItems();
        if(CollectionUtils.isNotEmpty(items)){
            for(CoefficientItem item : items) {
                modifyCoefficientItemMetaInfo(item, period, createdAt);
            }
        }
        return coefficientDO;
    }

    private void modifyCoefficientMetaInfo(CoefficientDO coefficientDO,String period, LocalDateTime createdAt) {
        if(coefficientDO==null){
            return;
        }
        coefficientDO.setId(null);
        coefficientDO.setPeriod(period);
        coefficientDO.setCreated(createdAt);
        coefficientDO.setModified(createdAt);
    }

    private void modifyCoefficientItemMetaInfo(CoefficientItem item,String period, LocalDateTime createdAt) {
        if (item == null) {
            return;
        }
        item.setId(null);
        item.setPeriod(period);
        item.setCreated(createdAt);
        item.setModified(createdAt);
    }
   

}

package com.jd.kf.oss.performance.domain.runtime.domain.performanceresult;

import java.util.List;

public interface IPerformanceResultRepository {
    /**
     * 保存
     * @param resultDO
     */
//    void save(PerformanceFactorResultDO resultDO);

    /**
     * 批量保存
     * @param resultDOS
     */
    void batchSave(List<PerformanceFactorResultDO> resultDOS);

    /**
     * 批量保存
     * @param resultDOS
     */
    void batchSavePlan(List<PerformanceResultDO> resultDOS);

    /**
     * 保存
     * @param tenantCode
     */
    PerformanceFactorResultDO query(String tenantCode, String period, Long taskId, String businessLineId, String erp, String factorId);

//    /**
//     * 计算结果
//     * @param resultDO
//     */
//    void save(PerformanceResultDO resultDO);
//    /**
//     * 保存
//     * @param tenantCode
//     */
//    PerformanceResultDO query(String tenantCode, String period, Long taskId, String businessLineId, String erp);

    /**
     * 查询
     * @param tenantCode
     * @param period
     * @param taskId
     * @param erps
     * @return
     */
    List<PerformanceFactorResultDO>  query(String tenantCode, String period, Long taskId, List<String> erps);
}

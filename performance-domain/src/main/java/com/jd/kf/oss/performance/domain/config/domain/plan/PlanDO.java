package com.jd.kf.oss.performance.domain.config.domain.plan;

import com.jd.kf.oss.performance.domain.config.DomainBaseEntity;
import com.jd.kf.oss.performance.domain.config.domain.formula.FormulaNode;
import com.jd.kf.oss.performance.domain.config.domain.formula.FormulaParameter;
import com.jd.kf.oss.performance.domain.config.domain.formula.FormulaParser;
import com.jd.kf.oss.performance.utils.SpringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

@EqualsAndHashCode(callSuper = true)
@Data
public class PlanDO extends DomainBaseEntity {
    private static final ScriptEngineManager manager = new ScriptEngineManager();
    private static final ScriptEngine engine = manager.getEngineByName("JavaScript");

    /**
     * 方案名称
     */
    private String name;

    /**
     * 方案类型
     */
    private String type;


    /**
     * 公式展示信息
     */
    private String formulaDisplayInfo;

    /**
     * 公式描述信息
     */
    private String displayInfo;

    /**
     * 公式
     */
    private String formula;

    /**
     * 保留几位小数
     */
    private Integer decimalPlaces;

    /**
     * 取整类型:0-四舍五入，1-向上取整，2-向下取整
     */
    private Integer roundType;

    /**
     * 绩效月
     */
    private String period;

    /**
     * 状态
     */
    private String status;

    public PlanDO(String tenantCode, String period, String code) {
        this.setTenantCode(tenantCode);
        this.setCode(code);
        this.period = period;
    }

    public PlanDO() {
    }

    /**
     * 持久化
     * @return
     */
    public Long save() {
        IPlanRepository planRepository = SpringUtils.getBean(IPlanRepository.class);
        planRepository.save(this);
        return getId();
    }

    /**
     * 查询方案
     * @return
     */
    public PlanDO load() {
        IPlanRepository planRepository = SpringUtils.getBean(IPlanRepository.class);
        return planRepository.queryByCode(this.getTenantCode(), period, this.getCode());
    }

    /**
     * 删除方案
     * @return
     */
    public Boolean delete() {
        IPlanRepository planRepository = SpringUtils.getBean(IPlanRepository.class);
        return planRepository.delete(getTenantCode(), period, getCode());
    }

    /**
     * 生成code
     * @return
     */
    public String buildCode() {
        if (StringUtils.isNotBlank(getCode())) {
            return getCode();
        }
        String code =  UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        return String.format("%s_%s", "plan", code);
    }


    /**
     * 获取所有的指标id
     */
    public List<String> getFactorCodes() throws Exception {
        checkFormulaLegal(formula);
        try {
            List<FormulaParameter> parameters = Lists.newArrayList();
            FormulaParser parser = new FormulaParser();
            FormulaNode ast = parser.parse(formula);
            if (Objects.nonNull(ast)) {
                parameters = ast.getAllFactor();
                return parameters.stream().map(FormulaParameter::getName).collect(Collectors.toList());
            }
        } catch (Exception e) {
            throw new Exception("公式解析失败" + formula);
        }
        return Lists.newArrayList();
    }

    private boolean checkFormulaLegal(String formula) throws Exception {
        try {
                FormulaParser parser = new FormulaParser();
                FormulaNode ast = parser.parse(formula);
                if (Objects.isNull(ast)) {
                    throw new Exception("公式解析失败" + formula);
                }
                String expression = String.valueOf(formula);
                for (FormulaParameter param : ast.getParams()) {
                   expression = expression.replace(param.getOriginText(), "1");
                }
                for (FormulaNode param : ast.getFunctions()) {
                    expression = expression.replace(param.getOriginText(), "1");
                }
                engine.eval(expression);
        } catch (Exception e) {
            throw new Exception("公式解析失败" + formula);
        }
        return false;
    }

//    /**
//     * 获取所有的指标id
//     */
//    public List<String> getAllCoefficientCodes() throws Exception {
//        try {
//            List<FormulaParameter> parameters = Lists.newArrayList();
//            FormulaParser parser = new FormulaParser();
//            FormulaNode ast = parser.parse(formula);
//            if (Objects.nonNull(ast)) {
//                parameters = ast.getAllCoefficient();
//                return parameters.stream().map(FormulaParameter::getName).collect(Collectors.toList());
//            }
//        } catch (Exception e) {
//            throw new Exception("公式解析失败" + formula);
//        }
//        return Lists.newArrayList();
//    }

}

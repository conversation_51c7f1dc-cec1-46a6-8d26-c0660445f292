package com.jd.kf.oss.performance.domain.runtime.domain.customfunction;

import com.google.common.collect.Sets;
import com.jd.dal.common.utils.CollectionUtils;
import com.jd.dal.common.utils.StringUtils;
import com.jd.kf.oss.performance.domain.config.domain.formula.FormulaNode;
import com.jd.kf.oss.performance.domain.config.domain.formula.FormulaParameter;
import com.jd.kf.oss.performance.domain.runtime.aggregate.composite.PerformanceTaskItemContext;
import com.jd.kf.oss.performance.domain.runtime.domain.TaskElementTypeEnum;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceresult.PerformanceProcessResult;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceuser.PerformanceUserDO;
import com.jd.kf.oss.performance.domain.runtime.domain.plan.PerformanceFactor;
import com.jd.kf.oss.performance.domain.runtime.domain.plan.PerformanceIndex;
import com.jd.kf.oss.performance.enums.MetaTypeCodeEnum;
import javafx.util.Pair;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class RankCustomFunctionService {
    // 重复进线率要正序排序，本期先hard code, 后续改成功能。在指标上加上标识 / 使用ducc配置
    private static final Set<String> metricSets = Sets.newHashSet("uad_repeat_enter_ratio");
    public String calc(PerformanceUserDO erp, FormulaNode formulaNode, PerformanceProcessResult parentProcessResult, PerformanceTaskItemContext context) throws Exception {
        if (CollectionUtils.isEmpty(formulaNode.getParams()) || CollectionUtils.size(formulaNode.getParams()) != 1) {
            return "";
        }
        String preCalcResult = erp.getFactorValue(context.getPerformanceTaskItemDO().getBusinessLineId(), formulaNode.originText);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(preCalcResult)) {
            return preCalcResult;
        }
        List<PerformanceUserDO> userDOList = Lists.newArrayList();
        Optional<PerformanceUserDO> userDOOptional = context.getUserDOList().stream().filter(a -> a.getErp().equals(erp.getErp())).findFirst();
        if (userDOOptional.isPresent()) {
            userDOList = context.getUserDOList();
        } else {
            userDOList = context.getAssistUserDOList();
        }
        PerformanceProcessResult processResult = build(erp, formulaNode.originText, formulaNode.originText, context);
        Boolean reverseOrder = true;
        String tenantCode = context.getPerformanceTaskItemDO().getTenantCode();
        String period = context.getPerformanceTargetDO().getPeriod();
        FormulaParameter parameter = formulaNode.getParams().get(0);
        List<Pair<String, String>> pairs = Lists.newArrayList();
        Integer cnt = 0;
        if (MetaTypeCodeEnum.FACTOR.getCode().equals(parameter.type)) {
            PerformanceFactor factor = context.getPerformanceFactorMap().get(PerformanceFactor.buildKey(tenantCode, period, parameter.getName()));
            for (PerformanceUserDO performanceUserDO : userDOList) {
                String value = factor.calc(performanceUserDO, processResult, context);
                if (StringUtils.isBlank(value) || Objects.isNull(value) || StringUtils.equalsIgnoreCase("null", value)) {
                    value = String.valueOf(Double.MAX_VALUE);
                    ++cnt;
                }
                pairs.add(new Pair<>(value, performanceUserDO.getErp()));
            }
        } else if (MetaTypeCodeEnum.INDEX.getCode().equals(parameter.type)) {
            PerformanceIndex index = context.getPerformanceIndexMap().get(PerformanceIndex.buildKey(tenantCode, period, parameter.getName()));
            if (Objects.isNull(index)) {
                return "0";
            }
            // 非逆序
            reverseOrder = !metricSets.contains(index.getKpiCd());
            for (PerformanceUserDO performanceUserDO : userDOList) {
                String value = index.calc(performanceUserDO, context);
                if (StringUtils.isBlank(value)) {
                    if (reverseOrder) {
                        value = String.valueOf(Double.MIN_VALUE);
                    } else {
                        value = String.valueOf(Double.MAX_VALUE);
                    }
                    ++cnt;
                }
                pairs.add(new Pair<>(value, performanceUserDO.getErp()));
            }
        }

        Map<String, PerformanceUserDO> userDOMap = userDOList.stream().collect(Collectors.toMap(PerformanceUserDO::getErp, PerformanceUserDO->PerformanceUserDO, (a, b) -> a));

        String result = null;
        Integer pos = 0;
        Boolean status = true;
        if (userDOList.size() != cnt) {
            sortPairs(pairs, reverseOrder);
            for (int i = 0; i < pairs.size(); i++) {
                PerformanceUserDO userDO = userDOMap.get(pairs.get(i).getValue());
                pos = i + 1;
                String result2 = String.valueOf((Double.valueOf(i + 1)) / (Double.valueOf(pairs.size())));
                PerformanceProcessResult processResult1 = build(userDO, formulaNode.originText, formulaNode.originText, context);
                addProcess(userDO, parentProcessResult, processResult1, result2, String.format("%s / %s", pos, pairs.size()), status);
                if (StringUtils.equalsIgnoreCase(pairs.get(i).getValue(), erp.getErp())) {
                    result = result2;
                }
            }
        } else {
            status = false;
        }
        addProcess(erp, parentProcessResult, processResult, result, String.format("%s / %s", pos, pairs.size()), status);
        return result;
    }

    private void addProcess(PerformanceUserDO erp,PerformanceProcessResult parentProcessResult, PerformanceProcessResult processResult, String result, String detail, Boolean status) {
        processResult.setResult(String.valueOf(result));
        processResult.setDetail(detail);
        processResult.setStatus(status ? "已完成" : "未完成");
        //parentProcessResult.getProcessResultList().add(processResult);
        erp.addProcess(processResult);
    }

    /**
     * 排序
     * @param pairs
     */
    private static void sortPairs(List<Pair<String, String>> pairs, Boolean reverseOrder) {
        Collections.sort(pairs, new Comparator<Pair<String, String>>() {
            @Override
            public int compare(Pair<String, String> p1, Pair<String, String> p2) {
                // 处理空字符串分数为0的情况
                String score1 = p1.getKey().isEmpty() ? "0" : p1.getKey();
                String score2 = p2.getKey().isEmpty() ? "0" : p2.getKey();

                // 将字符串分数转换为整数比较
                if (reverseOrder) {
                    return Double.compare(Double.parseDouble(score1), Double.parseDouble(score2)) * -1;
                } else {
                    return Double.compare(Double.parseDouble(score1), Double.parseDouble(score2));
                }
            }
        });
    }

    private PerformanceProcessResult build(PerformanceUserDO erp, String code, String formula, PerformanceTaskItemContext context) {
        return new PerformanceProcessResult(
                context.getPerformanceTaskItemDO().getTenantCode(),
                context.getPerformanceTaskItemDO().getPeriod(),
                context.getPerformanceTaskItemDO().getBusinessLineId(),
                context.getPerformanceTaskItemDO().getBusinessLineName(),
                context.getPerformanceTaskItemDO().getTaskId(),
                TaskElementTypeEnum.FACTOR,
                erp.getErp(),
                code,
                formula
        );
    }
}

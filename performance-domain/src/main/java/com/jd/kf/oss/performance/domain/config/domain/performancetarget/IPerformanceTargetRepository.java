package com.jd.kf.oss.performance.domain.config.domain.performancetarget;

import com.jd.kf.oss.performance.domain.config.domain.businessLine.BusinessLineDO;
import com.jd.kf.oss.performance.utils.CommonPage;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/06/25
 */
public interface IPerformanceTargetRepository {
    // CommonPage<PerformanceTargetDO> pageTarget(PageCommand<PerformanceTargetDO> pageCommand);

    /**
     * 根据条件分页查询绩效目标
     * @param tenantCode 租户标识
     * @param period 绩效月，可选
     * @param businessLineId 绩效组ID，可选
     * @param businessLineName 绩效组名称，可选
     * @param pageNum 页码
     * @param pageSize 页面大小
     * @return 分页结果
     */
    CommonPage<PerformanceTargetDO> queryTargetByConditions(String tenantCode, String period, String businessLineId, String businessLineName, String planCode,int pageNum, int pageSize);

    PerformanceTargetDO queryTargetByBusinessLineId(String businessLineId, String tenantCode, String period);

    PerformanceTargetDO queryTargetByBusinessLineIdAndPeriod(String businessLineId,String period);


    List<PerformanceTargetDO>  listTargetByBusinessLineCodeAndPeriod(List<String> businessCodes,String period);

    /**
     * 更新绩效目标
     * @param targetDO
     * @return
     */
    boolean updateTargetByLineIdAndPeriod(PerformanceTargetDO targetDO);

    boolean saveTarget(PerformanceTargetDO targetDO);

    /**
     * 批量更新绩效目标
     * @param targetDOs
     * @return
     */
    boolean updateBatchTargetById(List<PerformanceTargetDO> targetDOs);

    /**
     * 查询全量目标
     * @param period
     * @return
     */
    List<PerformanceTargetDO> queryAllTargetByPeriod(String period);

    /**
     * 批量保存
     * @param targets
     * @return
     */
    boolean saveBatchTargets(List<PerformanceTargetDO> targets);

    /**
     * 根据targetID更新绩效目标
     * @param targetIds
     * @param planCode
     */
    void updateTargetPlanCode(List<Long> targetIds,String planCode);


    /**
     * 更新绩效组修改时间
     * @param businessLineIds 业务线ID列表
     * @param period 时间段标识
     * @param operator 操作人员标识
     */
    void updateTargetModifyTime(List<String> businessLineIds,String period, String operator);
}

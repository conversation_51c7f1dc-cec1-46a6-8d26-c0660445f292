package com.jd.kf.oss.performance.domain.runtime.domain;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TaskItemStatusEnum {
    NOT_STARTED(0, "未开始"),
    FACTOR_RUNNING(1, "因子运行"),
    FACTOR_RUNNING_COMPLETED(1, "因子运行结束"),
    PLAN_RUNNING(1, "方案运行开始"),
    COMPLETED(2, "已结束"),
    CANCELLED(3, "已取消");

    private final int code;

    private final String status;
}

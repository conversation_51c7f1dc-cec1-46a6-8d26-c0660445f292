package com.jd.kf.oss.performance.domain.runtime.domain;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TaskStatusEnum {
    NOT_STARTED(0, "未开始"),
    FACTOR_RUNNING(1, "运行中"),
    FACTOR_RUNNING_COMPLETED(2, "指标运行结束"),
    PLAN_RUNNING(3, "方案运行开始"),
    COMPLETED(4, "已结束"),
    CANCELLED(5, "已取消");

    private final int code;

    private final String status;
}

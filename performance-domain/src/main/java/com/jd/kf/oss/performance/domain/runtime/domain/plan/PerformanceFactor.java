package com.jd.kf.oss.performance.domain.runtime.domain.plan;

import com.google.common.collect.Maps;
import com.jd.kf.oss.performance.domain.config.domain.formula.FormulaNode;
import com.jd.kf.oss.performance.domain.config.domain.formula.FormulaParameter;
import com.jd.kf.oss.performance.domain.config.domain.formula.FormulaParser;
import com.jd.kf.oss.performance.domain.config.domain.performancetarget.PerformanceTargetDO;
import com.jd.kf.oss.performance.domain.runtime.RuntimeDomainBaseEntity;
import com.jd.kf.oss.performance.domain.runtime.aggregate.composite.PerformanceTaskItemContext;
import com.jd.kf.oss.performance.domain.runtime.domain.CustomFunctionTypeEnum;
import com.jd.kf.oss.performance.domain.runtime.domain.RoundTypeEnum;
import com.jd.kf.oss.performance.domain.runtime.domain.TaskElementTypeEnum;
import com.jd.kf.oss.performance.domain.runtime.domain.TaskItemStatusEnum;
import com.jd.kf.oss.performance.domain.runtime.domain.customfunction.MatchCustomFunctionService;
import com.jd.kf.oss.performance.domain.runtime.domain.customfunction.RankCustomFunctionService;
import com.jd.kf.oss.performance.domain.runtime.domain.customfunction.SumCustomFunctionService;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceresult.PerformanceFactorResultDO;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceresult.PerformanceProcessResult;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceuser.PerformanceUserDO;
import com.jd.kf.oss.performance.enums.FactorSysEnum;
import com.jd.kf.oss.performance.enums.MetaTypeCodeEnum;
import com.jd.kf.oss.performance.utils.SpringUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Data
public class PerformanceFactor extends RuntimeDomainBaseEntity {
    private static final ScriptEngineManager manager = new ScriptEngineManager();
    private static final ScriptEngine engine = manager.getEngineByName("JavaScript");
    /**
     * code
     */
    private String code;

    /**
     * 因子名称
     */
    private String name;

    /**
     * 因子类型
     */
    private String type;

    /**
     * 公式
     */
    private String formula;

    /**
     * 保留几位小数
     */
    private Integer decimalPlaces;

    /**
     * 取整类型:0-四舍五入，1-向上取整，2-向下取整
     */
    private RoundTypeEnum roundType;

    /**
     * 绩效月
     */
    private String period;

    public String buildKey() {
        String key = "Factor_tenantCode:%s_period:%s_code:%s";
        return String.format(key, getTenantCode(), period, code);
    }

    public static String buildKey(String tenantCode, String period, String code) {
        String key = "Factor_tenantCode:%s_period:%s_code:%s";
        return String.format(key, tenantCode, period, code);
    }


    public String calc(PerformanceUserDO erp, PerformanceProcessResult parentProcessResult, PerformanceTaskItemContext context) throws Exception {
        String preCalcResult = erp.getFactorValue(erp.getBusinessLineId(), code);
        if (StringUtils.isNotBlank(preCalcResult)) {
            return preCalcResult;
        }
        if (FactorSysEnum.getByCode(code) != null) {
            if (context.getPerformanceTaskItemDO().getStatus().equals(TaskItemStatusEnum.PLAN_RUNNING)) {
                return calcSupport(erp, code, erp.getBusinessLineId(), parentProcessResult, context);
            }
        }
        PerformanceProcessResult processResult = build(erp, code, context);
        FormulaParser parser = new FormulaParser();
        FormulaNode ast = parser.parse(formula);
        if (Objects.isNull(ast)) {
            return "";
        }
        Map<String, String> paramResultMap = Maps.newHashMap();
        for (FormulaParameter param : ast.getParams()) {
            String value = "";
            if (MetaTypeCodeEnum.FACTOR.getCode().equals(param.type)) {
                PerformanceFactor factor = context.getPerformanceFactorMap().get(PerformanceFactor.buildKey(getTenantCode(), getPeriod(), param.getName()));
                value = factor.calc(erp, processResult, context);
            }
            if (MetaTypeCodeEnum.INDEX.getCode().equals(param.type)) {
                value = SpringUtils.getBean(PerformanceIndexService.class).calcIndex(param.getName(), erp, processResult, context);
            }
            if (MetaTypeCodeEnum.COEFFICIENT.getCode().equals(param.getType())) {
                PerformanceCoefficientDO coefficientDO = context.getCoefficientDOMap().get(PerformanceCoefficientDO.buildKey(getTenantCode(), getPeriod(), param.getName()));
                value = coefficientDO.getCoefficientItems().get(0).getCoefficientNum();
            }
            paramResultMap.put(param.getOriginText(), value);
        }

        for (FormulaNode node : ast.getFunctions()) {
            String value = "";
            if (StringUtils.equalsAnyIgnoreCase(CustomFunctionTypeEnum.MATCH.getCode(), node.getName())) {
                value = Objects.requireNonNull(SpringUtils.getBean(MatchCustomFunctionService.class)).calc(erp, node, processResult, context);
            }
            if (StringUtils.equalsAnyIgnoreCase(CustomFunctionTypeEnum.SUM.getCode(), node.getName())) {
                value = Objects.requireNonNull(SpringUtils.getBean(SumCustomFunctionService.class)).calc(erp, node, processResult, context);
            }
            if (StringUtils.equalsAnyIgnoreCase(CustomFunctionTypeEnum.RANK.getCode(), node.getName())) {
                value = Objects.requireNonNull(SpringUtils.getBean(RankCustomFunctionService.class)).calc(erp, node, processResult, context);
            }
            paramResultMap.put(node.getOriginText(), value);
        }
        String expression = String.valueOf(formula);
        Boolean flag = true;
        for (Map.Entry<String, String> entry : paramResultMap.entrySet()) {
            String value = entry.getValue();
            if (Objects.nonNull(value) && StringUtils.isBlank(entry.getValue())) {
                value = "0";
            }
            if (StringUtils.equalsAnyIgnoreCase(value, "null") || Objects.isNull(value)) {
                value = "0";
                flag = false;
            }
            expression = expression.replace(entry.getKey(), value);
        }
        expression = expression;
        Object result = null;
        Boolean status = true;
        if (expression.contains("(") || expression.contains(")") || expression.contains("{") || expression.contains("}")) {
            status = false;
            result = null;
        } else {
            try {
                result = engine.eval(expression + "* 1.0");
            } catch (Exception e) {
                status = false;
                log.info("calc error:{}", erp.getErp(), e);
            }
        }
        if ((!flag || result == null || Double.isNaN((Double) result)) || (Objects.nonNull(FactorSysEnum.getByCode(code)) && context.getPerformanceTaskItemDO().getStatus().equals(TaskItemStatusEnum.FACTOR_RUNNING))) {
            status = false;
            result = null;
        }
        String res = roundType.processNumber(String.valueOf(result), decimalPlaces);
        addProcess(erp, parentProcessResult, processResult, name, expression, res, status, paramResultMap);
        return res;
    }

    private void addProcess(PerformanceUserDO userDO, PerformanceProcessResult parentProcessResult, PerformanceProcessResult processResult, String name, String expression, String result, Boolean status, Map<String, String> paramResultMap) {
        StringBuilder sb = new StringBuilder();
        sb.append(name ).append(formula).append(String.format("\n%s = %s\n", expression, result));
        paramResultMap.forEach((key, value) -> sb.append(String.format("\n%s = %s", key, value)));
        processResult.setResult(String.valueOf(result));
        processResult.setDetail(sb.toString());
        processResult.setStatus(status ? "已完成" : "未完成");
        userDO.addProcess(processResult);
    }

    private PerformanceProcessResult build(PerformanceUserDO erp, String code, PerformanceTaskItemContext context) {
        return new PerformanceProcessResult(
                getTenantCode(),
                period,
                context.getPerformanceTaskItemDO().getBusinessLineId(),
                context.getPerformanceTaskItemDO().getBusinessLineName(),
                context.getPerformanceTaskItemDO().getTaskId(),
                TaskElementTypeEnum.FACTOR,
                erp.getErp(),
                code,
                formula
        );
    }

    private String calcSupport(PerformanceUserDO userDO, String factorCode, String businessLineId, PerformanceProcessResult parentProcessResult, PerformanceTaskItemContext context) throws Exception{
        FactorSysEnum factorSysEnum = FactorSysEnum.getByCode(factorCode);
        String value = "";
        if (context.getPerformanceTaskItemDO().getStatus().equals(TaskItemStatusEnum.FACTOR_RUNNING)) {
            return "";
        } else if (context.getPerformanceTaskItemDO().getStatus().equals(TaskItemStatusEnum.PLAN_RUNNING)) {
            if (FactorSysEnum.SUPPORT_BUSINESS_CPD.equals(factorSysEnum)) {
                value = calcSupportBusinessLineCpd(userDO, FactorSysEnum.SUPPORT_BUSINESS_CPD, businessLineId, parentProcessResult, context);
            } else if (FactorSysEnum.SUPPORT_BUSINESS_LINE_QUANTITY.equals(factorSysEnum)) {
                value = calcSupportConversionQuantity(userDO, FactorSysEnum.SUPPORT_BUSINESS_LINE_QUANTITY, businessLineId, parentProcessResult, context);
            } else if (FactorSysEnum.SUPPORT_MAIN_PRICE.equals(factorSysEnum)) {
                value = calcSupportPrice(userDO, FactorSysEnum.SUPPORT_MAIN_PRICE, businessLineId, parentProcessResult, context);
            } else if (FactorSysEnum.COMPREHENSIVE_QUALITY_COEFFICIENT.equals(factorSysEnum)) {
                value = calcComprehensiveQualityCoefficient(userDO, FactorSysEnum.COMPREHENSIVE_QUALITY_COEFFICIENT, parentProcessResult, context);
            } else if (FactorSysEnum.SUPPORT_QUANTITY.equals(factorSysEnum)) {
                value = calcSupportQuantity(userDO, FactorSysEnum.SUPPORT_QUANTITY, businessLineId, parentProcessResult, context);
            } else if (FactorSysEnum.SUPPORT_CONVERSION_TOTAL.equals(factorSysEnum)) {
                value = calcSupportConversionTotal(userDO, FactorSysEnum.SUPPORT_CONVERSION_TOTAL,businessLineId, parentProcessResult, context);
            }
        }
        return roundType.processNumber(value, decimalPlaces);
    }


    /**
     * 支援赛道单量
     * @param userDO
     * @param parentProcessResult
     * @param context
     * @return
     */
    private String calcSupportQuantity(PerformanceUserDO userDO, FactorSysEnum factorSysEnum, String businessLineId, PerformanceProcessResult parentProcessResult, PerformanceTaskItemContext context) {
        PerformanceProcessResult processResult = userDO.getFactorResultMap().get(PerformanceProcessResult.buildKey(getTenantCode(), period, businessLineId, FactorSysEnum.SUPPORT_QUANTITY.getRelatedCode(), userDO.getErp()));
        if (Objects.isNull(processResult)) {
            return "";
        }
        PerformanceProcessResult processResult2 = build(userDO, factorSysEnum.getCode(), context);
        Map<String, String> paramResultMap = Maps.newHashMap();
        paramResultMap.put(String.format("{%s}", factorSysEnum.getCode()), processResult.getResult());
        addProcess(userDO, parentProcessResult, processResult2, factorSysEnum.getDesc(), factorSysEnum.getCode(), processResult.getResult(), true, paramResultMap);
        return processResult.getResult();
    }

    /**
     * 支援折算总量
     * @param userDO
     * @param parentProcessResult
     * @param context
     * @return
     */
    private String calcSupportConversionTotal(PerformanceUserDO userDO, FactorSysEnum factorSysEnum, String businessLineId, PerformanceProcessResult parentProcessResult, PerformanceTaskItemContext context) throws Exception {
        PerformanceProcessResult processResult = build(userDO, factorSysEnum.getCode(), context);
        List<String> businessLineIds = userDO.getSupportBusinessLineIds();
        List<String> values = Lists.newArrayList();
        Map<String, String> params = Maps.newHashMap();
        for (String lineId : businessLineIds) {
            String lineIdValue = calcSupportConversionQuantity(userDO, FactorSysEnum.SUPPORT_BUSINESS_LINE_QUANTITY, lineId, processResult, context);
            values.add(lineIdValue);
            params.put(String.format("support businessLineId:{%s}, {%s}", lineId, FactorSysEnum.SUPPORT_BUSINESS_LINE_QUANTITY.getCode()), lineIdValue);
        }
        Double res = 0.0;
        for (String value : values) {
            if (StringUtils.isNotBlank(value)) {
                res += Double.valueOf(value);
            }
        }
        addProcess(userDO, parentProcessResult, processResult, factorSysEnum.getDesc(), String.format("{%s}", factorSysEnum.getCode()), String.valueOf(res), true, params);
        return roundType.processNumber(String.valueOf(res), decimalPlaces);
    }

    /**
     * 支援赛道CPD
     * @param userDO
     * @param parentProcessResult
     * @param context
     * @return
     */
    private String calcSupportBusinessLineCpd(PerformanceUserDO userDO, FactorSysEnum factorSysEnum, String businessLineId, PerformanceProcessResult parentProcessResult, PerformanceTaskItemContext context) {
        PerformanceTargetDO targetDO = context.getTargetDOMap().get(businessLineId);
        PerformanceProcessResult processResult2 = build(userDO, factorSysEnum.getCode(), context);
        Map<String, String> paramResultMap = Maps.newHashMap();
        paramResultMap.put(String.format("{%s}", factorSysEnum.getCode()), targetDO.getCpd());
        addProcess(userDO, parentProcessResult, processResult2,  factorSysEnum.getDesc(), factorSysEnum.getCode(), targetDO.getCpd(), true, paramResultMap);
        return targetDO.getCpd();
    }

    /**
     * 支援折算量
     *
     * @param userDO
     * @param parentProcessResult
     * @param context
     * @return
     */
    private String calcSupportConversionQuantity(PerformanceUserDO userDO,
                                                 FactorSysEnum factorSysEnum,
                                                 String businessLineId,
                                                 PerformanceProcessResult parentProcessResult,
                                                 PerformanceTaskItemContext context) throws Exception {
        PerformanceProcessResult processResult = build(userDO, factorSysEnum.getCode(), context);
        Map<String, String> paramResultMap = Maps.newHashMap();
        // 支援折算量
        String supportQuantity = calcSupport(userDO, FactorSysEnum.SUPPORT_QUANTITY.getCode(), businessLineId, processResult, context);
        paramResultMap.put(String.format("{%s}", FactorSysEnum.SUPPORT_QUANTITY.getCode()), supportQuantity);
        // 主赛道cpd
        String primaryCpd = calcSupport(userDO, FactorSysEnum.SUPPORT_BUSINESS_CPD.getCode(), userDO.getBusinessLineId(),processResult, context);
        paramResultMap.put(String.format("主赛道cpd: {%s}", FactorSysEnum.SUPPORT_BUSINESS_CPD.getCode()), primaryCpd);
        // 支援赛道cpd
        String supportCpd = calcSupport(userDO, FactorSysEnum.SUPPORT_BUSINESS_CPD.getCode(), businessLineId,processResult, context);
        paramResultMap.put(String.format("支援赛道cpd: {%s}", FactorSysEnum.SUPPORT_BUSINESS_CPD.getCode()), supportCpd);

        Double cpd = 1.0;
        if (StringUtils.isNotBlank(primaryCpd) && StringUtils.isNotBlank(supportCpd)) {
            cpd = Double.parseDouble(primaryCpd) / Double.parseDouble(supportCpd);
        }
        PerformanceFactor factor = context.getPerformanceFactorMap().get(PerformanceFactor.buildKey(getTenantCode(), period, FactorSysEnum.SUPPORT_BUSINESS_LINE_QUANTITY.getCode()));
        FormulaParser parser = new FormulaParser();
        FormulaNode ast = parser.parse(factor.getFormula());
        if (Objects.isNull(ast)) {
            return "";
        }
        Boolean sameChannel = context.weatherSameChannel(userDO, businessLineId);
        String coefficient = "1.0";
        for (FormulaParameter param : ast.getParams()) {
            if (MetaTypeCodeEnum.COEFFICIENT.getCode().equals(param.getType()) && !sameChannel) {
                PerformanceCoefficientDO coefficientDO = context.getCoefficientDOMap().get(PerformanceCoefficientDO.buildKey(getTenantCode(), getPeriod(), param.getName()));
                coefficient = coefficientDO.getCoefficientItems().get(0).getCoefficientNum();
                paramResultMap.put(param.originText, coefficient);
            }
        }
        Double res;
        if (StringUtils.isBlank(supportQuantity) || StringUtils.isBlank(coefficient)) {
            res = 0.0;
        } else {
            res = Double.parseDouble(supportQuantity) * cpd * Double.parseDouble(coefficient);
        }
        addProcess(userDO, parentProcessResult, processResult,  factorSysEnum.getDesc(), factorSysEnum.getCode(), String.valueOf(res), true, paramResultMap);
        return roundType.processNumber(String.valueOf(res), decimalPlaces);
    }


    /**
     * 支援赛道单价
     * factor_support_main_price
     * @param userDO
     * @param parentProcessResult
     * @param context
     * @return
     */
    private String calcSupportPrice(PerformanceUserDO userDO, FactorSysEnum factorSysEnum, String businessLineId, PerformanceProcessResult parentProcessResult, PerformanceTaskItemContext context) {
        PerformanceTargetDO targetDO = context.getTargetDOMap().get(businessLineId);
        PerformanceProcessResult processResult2 = build(userDO, factorSysEnum.getCode(), context);
        Map<String, String> paramResultMap = Maps.newHashMap();
        paramResultMap.put(String.format("{%s}", factorSysEnum.getCode()), targetDO.getPrice());
        addProcess(userDO, parentProcessResult, processResult2,  factorSysEnum.getDesc(), factorSysEnum.getCode(), targetDO.getPrice(), true, paramResultMap);
        return targetDO.getPrice();
    }


    /**
     * 综合质量系数
     * factor_support_main_price
     * @param userDO
     * @param parentProcessResult
     * @param context
     * @return
     */
    private String calcComprehensiveQualityCoefficient(PerformanceUserDO userDO, FactorSysEnum factorSysEnum, PerformanceProcessResult parentProcessResult, PerformanceTaskItemContext context) throws Exception {
        PerformanceProcessResult processResult2 = build(userDO, factorSysEnum.getCode(), context);
        List<String> businessLineIds = userDO.getSupportBusinessLineIds();
        businessLineIds.add(userDO.getBusinessLineId());
        // 新人加速系数
        String code = "factor_new_employee_accerleration_coefficient";
        PerformanceFactor factor = context.getPerformanceFactorMap().get(PerformanceFactor.buildKey(getTenantCode(), period, code));
        String coe = factor.calc(userDO, processResult2, context);

        Map<String, String> paramResultMap = Maps.newHashMap();
        for (String businessLineId : businessLineIds) {
            String value = calcSingleQualityCoefficient(userDO, factorSysEnum, businessLineId, processResult2, context , coe);
            paramResultMap.put(String.format("赛道:%s 系数", businessLineId), value);
        }
        Double result = 0.0;
        for (String value : paramResultMap.values()) {
            if (StringUtils.isNotBlank(value)) {
                result += Double.parseDouble(value);
            }
        }
        String res = roundType.processNumber(String.valueOf(result), decimalPlaces);
        addProcess(userDO, parentProcessResult, processResult2,  factorSysEnum.getDesc(), factorSysEnum.getCode(), String.valueOf(res), true, paramResultMap);
        return res;
    }

    /**
     * 计算单个系数
     * @param userDO
     * @param factorSysEnum
     * @param businessLineId
     * @param parentProcessResult
     * @param context
     * @param coeStr
     * @return
     * @throws Exception
     */
    private String calcSingleQualityCoefficient(PerformanceUserDO userDO, FactorSysEnum factorSysEnum, String businessLineId, PerformanceProcessResult parentProcessResult, PerformanceTaskItemContext context, String coeStr) throws Exception {
        PerformanceProcessResult processResult2 = build(userDO, factorSysEnum.getCode(), context);
        // 排名赛道质量系数
        String code = "factor_rank_business_line_quntity_coefficient";
        Map<String, String> paramResultMap = Maps.newHashMap();
        // 排名赛道质量系数
        String rankCoeStr = userDO.getFactorValue(businessLineId, code);
        paramResultMap.put(String.format("排名赛道质量系数: {%s}",FactorSysEnum.SUPPORT_CONVERSION_TOTAL.getCode()), rankCoeStr);
        Double rankCoe = StringUtils.isBlank(rankCoeStr) ? 0.0 : Double.parseDouble(rankCoeStr);

        // 主赛道单量
        String primaryLineValueStr = calcSupport(userDO, FactorSysEnum.SUPPORT_BUSINESS_LINE_QUANTITY.getCode(), userDO.getBusinessLineId(), processResult2, context);
        paramResultMap.put(String.format("%s: {%s}", "主赛道单量", FactorSysEnum.SUPPORT_BUSINESS_LINE_QUANTITY.getDesc()), primaryLineValueStr);
        Double primaryLineValue = StringUtils.isBlank(primaryLineValueStr) ? 0.0 : Double.parseDouble(primaryLineValueStr);

        // 支援赛道折算单量
        String numeratorStr = calcSupport(userDO, FactorSysEnum.SUPPORT_BUSINESS_LINE_QUANTITY.getCode(), businessLineId, processResult2, context);
        paramResultMap.put(String.format("%s: {%s}", FactorSysEnum.SUPPORT_BUSINESS_LINE_QUANTITY.getDesc(),FactorSysEnum.SUPPORT_BUSINESS_LINE_QUANTITY.getCode()), numeratorStr);
        Double numerator = StringUtils.isBlank(numeratorStr) ? 0.0 : Double.parseDouble(numeratorStr);

        // 支援赛道则损系数
        String supportStr = calcSupport(userDO, FactorSysEnum.SUPPORT_CONVERSION_TOTAL.getCode(), businessLineId, processResult2, context);
        paramResultMap.put(String.format("%s: {%s}", FactorSysEnum.SUPPORT_CONVERSION_TOTAL.getDesc(),FactorSysEnum.SUPPORT_CONVERSION_TOTAL.getCode()), numeratorStr);
        Double support = StringUtils.isBlank(supportStr) ? 0.0 : Double.parseDouble(supportStr);

        // 普通抽掉量
        String normalQuantityCode = "factor_normal_withdrawal_quantity";
        String normalQuantityStr = userDO.getFactorValue(userDO.getBusinessLineId(), normalQuantityCode);
        Double normalQuantity = StringUtils.isBlank(normalQuantityStr) ? 0.0 : Double.parseDouble(normalQuantityStr);
        paramResultMap.put(String.format("%s: {%s}", "普通抽掉量", normalQuantityCode), normalQuantityStr);

        // 激励抽调量
        String inspireQuantityCode = "factor_inspire_withdrawal_quantity";
        String inspireQuantityStr = userDO.getFactorValue(userDO.getBusinessLineId(), inspireQuantityCode);
        Double inspireQuantity = StringUtils.isBlank(inspireQuantityStr) ? 0.0 : Double.parseDouble(inspireQuantityStr);
        paramResultMap.put(String.format("%s: {%s}", "激励抽调量", inspireQuantityCode), inspireQuantityStr);

        // 新人加速系数
        Double coe = StringUtils.isBlank(coeStr) ? 1 : Double.parseDouble(coeStr);
        paramResultMap.put(String.format("%s: {%s}", "新人加速系数", "factor_new_employee_accerleration_coefficient"), coeStr);

        Double result = 0.0;
        String expression = "businessLineId:{} 排名赛道质量系数 / ((支援赛道单量) / (主赛道单量 * 新人加速系数 + 支援折算总量 * 新人加速系数))";
        if (StringUtils.equalsAnyIgnoreCase(businessLineId, userDO.getBusinessLineId())) {
            result = rankCoe * ((numerator + normalQuantity + inspireQuantity) / (primaryLineValue * coe + support * coe + normalQuantity + inspireQuantity));
            expression = "businessLineId:{} 排名赛道质量系数 / ((主赛道单量 + 普通抽掉量 + 激励抽调量) / (主赛道单量 * 新人加速系数 + 支援折算总量 * 新人加速系数 + 普通抽掉量 + 激励抽调量))";
        } else {
            result = rankCoe * ((numerator) / (primaryLineValue * coe + support * coe + normalQuantity + inspireQuantity));
        }
        paramResultMap.put(String.format("{%s}", factorSysEnum.getCode()), String.valueOf(result));
        addProcess(userDO, parentProcessResult, processResult2,  factorSysEnum.getDesc(), String.format(expression, businessLineId), String.valueOf(result), true, paramResultMap);
        return String.valueOf(result);
        //return roundType.processNumber(String.valueOf(result), decimalPlaces);
    }

}

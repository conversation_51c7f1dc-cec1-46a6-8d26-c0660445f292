package com.jd.kf.oss.performance.domain.config.domain.appeal;

import com.jd.kf.oss.performance.utils.CheckUtil;
import com.jd.kf.oss.performance.utils.CommonPage;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 无效数据剔除领域服务
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Service
public class AppealRemoveDomainService {

    @Resource
    private IAppealRemoveRepository appealRemoveRepository;

    /**
     * 分页查询无效数据剔除（已废弃，使用字段参数版本）
     * @param pageCommand 分页查询命令
     * @return 分页结果
     */
    /*
    public CommonPage<AppealRemoveDO> pageAppealRemove(PageCommand<AppealRemoveDO> pageCommand) {
        CheckUtil.notNull(pageCommand, "分页查询命令不能为空");
        return appealRemoveRepository.pageAppealRemove(pageCommand);
    }
    */

    /**
     * 根据条件分页查询无效数据剔除
     * @param period 绩效月
     * @param kpiName 指标名称，可选
     * @param ticketId 需剔除的业务单号，可选
     * @param pageNum 页码
     * @param pageSize 页面大小
     * @return 分页结果
     */
    public CommonPage<AppealRemoveDO> queryAppealRemoveByConditions(String period, String kpiName, String ticketId,
                                                                    int pageNum, int pageSize) {
        CheckUtil.notBlank(period, "绩效月不能为空");
        CheckUtil.notNull(pageNum, pageSize, "分页查询参数不能为空");
        CheckUtil.isTrue(pageNum > 0, "页码必须大于0");
        CheckUtil.isTrue(pageSize > 0, "页面大小必须大于0");

        // 直接使用字段参数调用Repository，参数顺序与Repository完全一致
        return appealRemoveRepository.queryAppealRemoveByConditions(period, kpiName, ticketId, pageNum, pageSize);
    }

    /**
     * 批量删除无效数据剔除
     * @param ids 待删除ID列表
     * @return 是否成功
     */
    public boolean batchDelete(List<Long> ids) {
        CheckUtil.notEmpty(ids, "待删除ID列表不能为空");

        return appealRemoveRepository.batchDelete(ids);
    }

    /**
     * 根据条件查询无效数据剔除列表（用于导出）
     * @param period 绩效月
     * @param kpiCd 指标code，可选
     * @param erp 客服ERP，可选
     * @return 无效数据剔除列表
     */
    public List<AppealRemoveDO> queryAppealRemoveList(String period, String kpiCd, String erp) {
        CheckUtil.notBlank(period, "绩效月不能为空");
        
        return appealRemoveRepository.queryAppealRemoveList(period, kpiCd, erp);
    }

    /**
     * 批量保存无效数据剔除（用于导入）
     * @param appealRemoveList 无效数据剔除列表
     * @return 保存成功的数量
     */
    public int batchSave(List<AppealRemoveDO> appealRemoveList) {
        CheckUtil.notEmpty(appealRemoveList, "无效数据剔除列表不能为空");
        return appealRemoveRepository.batchSave(appealRemoveList);
    }

    /**
     * @param period
     * @param ticketId
     * @param kpiName
     * @return boolean
     * 判断唯一键是否存在
     */
    public boolean existsDuplicate(String period, String ticketId, String kpiName) {
        return appealRemoveRepository.existsDuplicate(period, ticketId, kpiName);
    }

}

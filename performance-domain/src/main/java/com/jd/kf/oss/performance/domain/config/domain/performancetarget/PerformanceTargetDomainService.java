package com.jd.kf.oss.performance.domain.config.domain.performancetarget;

import com.jd.dal.common.utils.CollectionUtils;
import com.jd.kf.oss.performance.context.UserContextHolder;
import com.jd.kf.oss.performance.exception.BizException;
import com.jd.kf.oss.performance.utils.CheckUtil;
import com.jd.kf.oss.performance.utils.CommonPage;
import com.jd.kf.oss.performance.utils.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/06/25
 */
@Service
public class PerformanceTargetDomainService {
    @Resource
    private IPerformanceTargetRepository targetRepository;

    /**
     * 根据条件分页查询绩效目标
     *
     * @param tenantCode       租户标识
     * @param period           绩效月，可选
     * @param businessLineId   绩效组ID，可选
     * @param businessLineName 绩效组名称，可选
     * @param pageNum          页码
     * @param pageSize         页面大小
     * @return 分页结果
     */
    public CommonPage<PerformanceTargetDO> queryTargetByConditions(String tenantCode, String period, String businessLineId, String businessLineName, String planCode, int pageNum, int pageSize) {
        CheckUtil.notBlank(tenantCode, "租户标识不能为空");
        CheckUtil.notNull(pageNum, pageSize, "分页查询参数不能为空");
        CheckUtil.isTrue(pageNum > 0, "页码必须大于0");
        CheckUtil.isTrue(pageSize > 0, "页面大小必须大于0");

        // 直接使用字段参数调用Repository，参数顺序与Repository完全一致
        return targetRepository.queryTargetByConditions(tenantCode, period, businessLineId, businessLineName, planCode, pageNum, pageSize);
    }

    /**
     * 查询多个绩效组该绩效月下的绩效目标
     *
     * @param period
     * @param businessLineIds
     * @return
     */
    public List<PerformanceTargetDO> queryTargetListByBusinessLineCodeAndPeriod(String period, List<String> businessLineIds) {
        if (CollectionUtils.isEmpty(businessLineIds) || StringUtils.isBlank(period)) {
            throw new IllegalArgumentException("绩效月/绩效组编码不能为空");
        }
        return targetRepository.listTargetByBusinessLineCodeAndPeriod(businessLineIds, period);
    }

    /**
     * 查询指定绩效组该绩效月下的绩效目标
     *
     * @param period
     * @param businessLineId
     * @return
     */
    public PerformanceTargetDO queryTargetByBusinessLineIdAndPeriod(String businessLineId, String period) {
        if (StringUtils.isBlank(businessLineId) || StringUtils.isBlank(period)) {
            throw new IllegalArgumentException("查询绩效目标失败，绩效组编码以及绩效月不能为空");
        }
        return targetRepository.queryTargetByBusinessLineIdAndPeriod(businessLineId, period);
    }


    public boolean updateTargetByLineIdAndPeriod(PerformanceTargetDO performanceTargetDO) {
        if (performanceTargetDO == null || performanceTargetDO.getBusinessLineId() == null || performanceTargetDO.getPeriod() == null) {
            throw new IllegalArgumentException("更新绩效目标失败，绩效目标对应绩效月以及绩效组不能为空");
        }
        return targetRepository.updateTargetByLineIdAndPeriod(performanceTargetDO);
    }

    /**
     * 根据绩效周期查询所有绩效目标数据
     *
     * @param period 绩效周期，不能为空
     * @return 指定绩效周期内的所有绩效目标数据列表
     */
    public List<PerformanceTargetDO> queryAllTargetByPeriod(String period) {
        if (StringUtils.isBlank(period)) {
            throw new IllegalArgumentException("绩效月不能为空");
        }
        return targetRepository.queryAllTargetByPeriod(period);
    }

    /**
     * 批量导入绩效目标
     */
    private void importTarget(List<PerformanceTargetDO> importTargetDOs, BiFunction<PerformanceTargetDO, PerformanceTargetDO, PerformanceTargetDO> updateFunction) {
        if (CollectionUtils.isEmpty(importTargetDOs)) {
            return;
        }
        List<String> businessLineIds = importTargetDOs.stream().map(PerformanceTargetDO::getBusinessLineId).distinct().collect(Collectors.toList());
        List<PerformanceTargetDO> targetDOsInDB = targetRepository.listTargetByBusinessLineCodeAndPeriod(businessLineIds, importTargetDOs.get(0).getPeriod());
        // 获取<绩效组id，数据库中主键id>映射
        Map<String, PerformanceTargetDO> lineIdAndDOMap = targetDOsInDB.stream().collect(Collectors.toMap(PerformanceTargetDO::getBusinessLineId, Function.identity(), (existing, replacement) -> existing));
        List<PerformanceTargetDO> toUpdateDOs = new ArrayList<>();
        List<PerformanceTargetDO> toSaveDOs = new ArrayList<>();

        for (PerformanceTargetDO importTargetDO : importTargetDOs) {
            PerformanceTargetDO targetDO = lineIdAndDOMap.get(importTargetDO.getBusinessLineId());
            if (targetDO == null) {
                toSaveDOs.add(importTargetDO);
            } else {
                toUpdateDOs.add(updateFunction.apply(targetDO, importTargetDO));
            }
        }
        // 批量保存绩效目标
        targetRepository.saveBatchTargets(toSaveDOs);
        // 根据id批量更新绩效目标
        targetRepository.updateBatchTargetById(toUpdateDOs);
    }


    @Transactional(rollbackFor = Exception.class)
    public void importTargetCPD(List<PerformanceTargetDO> importTargetDOs) {
        importTarget(importTargetDOs, this::updateFunctionForCPD);
    }


    @Transactional(rollbackFor = Exception.class)
    public void importTargetPriceAndDays(List<PerformanceTargetDO> importTargetDOs) {
        importTarget(importTargetDOs, this::updateFunctionForPriceAndDays);
    }


    /**
     * 更新CPD
     *
     * @param targetDOInDB
     * @param importTargetDO
     * @return
     */
    public PerformanceTargetDO updateFunctionForCPD(PerformanceTargetDO targetDOInDB, PerformanceTargetDO importTargetDO) {
        if (targetDOInDB == null || importTargetDO == null) {
            throw new IllegalArgumentException("更新绩效目标失败，绩效目标不能为空");
        }
        // 更新CPD,null则空字符串直接覆盖
        targetDOInDB.setCpd(Optional.ofNullable(importTargetDO.getCpd()).orElse(""));
        targetDOInDB.setModified(LocalDateTime.now());
        targetDOInDB.setEditor(UserContextHolder.getOperator());
        return targetDOInDB;
    }

    /**
     * // 更新单价和天数
     *
     * @param targetDOInDB
     * @param importTargetDO
     * @return
     */
    public PerformanceTargetDO updateFunctionForPriceAndDays(PerformanceTargetDO targetDOInDB, PerformanceTargetDO importTargetDO) {
        if (targetDOInDB == null || importTargetDO == null) {
            throw new IllegalArgumentException("更新绩效目标失败，绩效目标不能为空");
        }
        // 更新天数和单价 直接覆盖
        targetDOInDB.setDays(Optional.ofNullable(importTargetDO.getDays()).orElse(""));
        targetDOInDB.setPrice(Optional.ofNullable(importTargetDO.getPrice()).orElse(""));
        targetDOInDB.setModified(LocalDateTime.now());
        targetDOInDB.setEditor(UserContextHolder.getOperator());
        return targetDOInDB;
    }


    /**
     * 批量保存绩效目标数据
     *
     * @param targets 要保存的绩效目标数据列表，不能为null或空集合
     * @return 保存是否成功，true表示成功，false表示失败
     */
    public boolean saveBatchTargets(List<PerformanceTargetDO> targets) {
        if (targets == null || targets.isEmpty()) return true;
        return targetRepository.saveBatchTargets(targets);
    }


    /**
     * 批量更新绩效目标的绩效方案
     */
    public void updateTargetPlanCode(List<Long> targetIds, String planCode) {
        if (CollectionUtils.isEmpty(targetIds)) {
            return;
        }
        targetRepository.updateTargetPlanCode(targetIds, planCode);
    }

    public Boolean weatherAnyBusinessLineAssociatedThisPlan(String period, String planCode) {
        List<PerformanceTargetDO> targets = queryAllTargetByPeriod(period);
        if (CollectionUtils.isEmpty(targets)) {
            return false;
        }
        List<PerformanceTargetDO> results = targets.stream().filter(a -> StringUtils.equalsAnyIgnoreCase(a.getEvaluationPlanCode(), planCode)).collect(Collectors.toList());
        return CollectionUtils.isNotEmpty(results);
    }

    @Transactional(rollbackFor = Exception.class)
    public List<String> filterTargetAndAssociatePlan(List<String> businessLineIds, String period, String planCode) {
        if (CollectionUtils.isEmpty(businessLineIds)) {
            return new ArrayList<>(0);
        }
        CheckUtil.notBlank(period, planCode, "绩效月和绩效方案编码不能为空");
        // 定时任务会自动创建对应的绩效组的绩效目标
        List<PerformanceTargetDO> targetInDB = queryTargetListByBusinessLineCodeAndPeriod(period, businessLineIds);
        // 过滤需要更新的绩效组和targetId
        Map<String,Long> lineAndTargetId = filterBusinessTargetToUpdate(targetInDB,planCode);
        List<Long> targetIdsToUpdate = new ArrayList<>(lineAndTargetId.values());
        updateTargetPlanCode(targetIdsToUpdate, planCode);
        // 兜底创建绩效目标，避免同步任务没有创建绩效目标
        List<String> lineIdsWithoutTarget= createTargetForAssociate(targetInDB, businessLineIds, period, planCode);
        // 合并绩效组id 创建index
        List<String> lineIdsToCreateIndex =new ArrayList<>(lineAndTargetId.keySet());
        lineIdsToCreateIndex.addAll(lineIdsWithoutTarget);
        return lineIdsToCreateIndex;
    }

    /**
     * 兜底检查和创建绩效目标数据
     *
     * @return
     */
    private List<String> createTargetForAssociate(List<PerformanceTargetDO> targetInDB, List<String> businessLineIds, String period, String planCode) {
        if (CollectionUtils.isEmpty(businessLineIds)) {
            return new ArrayList<>(0);
        }
        Set<String> lineIdsSet = new HashSet<>(businessLineIds);
        // 如果数据库中已经存在的绩效目标数量等于传入的绩效组数量，则不需要创建新的绩效目标
        if (targetInDB!=null && targetInDB.size() == lineIdsSet.size()) {
            return new ArrayList<>(0);
        }
        // 剔除已存在的绩效目标
        Optional.ofNullable(targetInDB).ifPresent(targets ->
                targets.forEach(target -> lineIdsSet.remove(target.getBusinessLineId())));
        // 兜底创建绩效目标
        List<PerformanceTargetDO> toCreateTargets = new ArrayList<>(lineIdsSet.size());
        lineIdsSet.forEach(lineId-> {
            PerformanceTargetDO performanceTargetDO = buildAssociateTargetDO(lineId, period, planCode);
            toCreateTargets.add(performanceTargetDO);
        });
        // 批量插入绩效目标
        saveBatchTargets(toCreateTargets);
        return new ArrayList<>(lineIdsSet);
    }

    /**
     * 构建关联绩效目标数据对象
     * @param businessLineId 业务线ID
     * @param period 期间/周期
     * @param planCode 评估方案编码
     * @return 包含指定属性的绩效目标数据对象
     */
    private PerformanceTargetDO buildAssociateTargetDO(String businessLineId,String period, String planCode) {
        PerformanceTargetDO targetDO = new PerformanceTargetDO();
        targetDO.setPeriod(period);
        targetDO.setBusinessLineId(businessLineId);
        targetDO.setEvaluationPlanCode(planCode);
        return targetDO;
    }


    /**
     * 过滤需要更新的业务线目标数据，返回业务线ID与目标ID的映射关系
     * @param targetInDB 数据库中已有的绩效目标数据列表
     * @param planCode 当前评估计划编码
     * @return 业务线ID与对应目标ID的映射关系Map集合
     */
    private Map<String,Long> filterBusinessTargetToUpdate(List<PerformanceTargetDO> targetInDB, String planCode) {
        List<String> lineNamesAssociatedOtherPlan = new ArrayList<>();
        Map<String,Long> lineAndTargetId=new HashMap<>(targetInDB.size());
        for (PerformanceTargetDO target : targetInDB) {
            if (StringUtils.isNotBlank(target.getEvaluationPlanCode())) {
                if (!(Objects.equals(target.getEvaluationPlanCode(), planCode))) {
                    lineNamesAssociatedOtherPlan.add(target.getBusinessLineName());
                }
            } else {
                lineAndTargetId.put(target.getBusinessLineId(),target.getId());
            }
        }
        if (!(lineNamesAssociatedOtherPlan.isEmpty())) {
            String errorMsg = buildAssociateErrorMessage(lineNamesAssociatedOtherPlan);
            throw new BizException(errorMsg);
        }
        return lineAndTargetId;
    }

    private String buildAssociateErrorMessage(List<String> lineNamesAssociatedOtherPlan) {
        StringBuilder errorMessage = new StringBuilder("关联操作失败:");
        if (!lineNamesAssociatedOtherPlan.isEmpty()) {
            errorMessage.append("绩效组")
                    .append(StringUtils.join(lineNamesAssociatedOtherPlan, ","))
                    .append("已关联其他绩效方案。");
        }
        return errorMessage.toString();
    }

    public void updateModifyTimeAndOperator(List<String> businessLineIds, String period) {
       if(CollectionUtils.isEmpty(businessLineIds)) {
           return;
       }
       if(StringUtils.isBlank(period)) {
           throw new IllegalArgumentException("绩效月不能为空");
       }
       targetRepository.updateTargetModifyTime(businessLineIds, period, UserContextHolder.getOperator());
    }


}

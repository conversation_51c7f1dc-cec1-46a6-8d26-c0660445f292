package com.jd.kf.oss.performance.domain.runtime.domain.plan;

import com.jd.kf.oss.performance.domain.runtime.aggregate.composite.PerformanceTaskItemContext;
import com.jd.kf.oss.performance.domain.runtime.domain.TaskElementTypeEnum;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceIndexData.WaiterHourAdjustment;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceresult.PerformanceProcessResult;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceuser.PerformanceUserDO;
import com.jd.kf.oss.performance.enums.IndexConstant;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
public class PerformanceIndexService {
    @Resource
    private IPerformanceIndexRepository performanceIndexRepository;

    /**
     * 根据业务线查询
     * @param tenantCode
     * @param period
     * @param businessLineId
     * @return
     */
    public List<PerformanceIndex> queryByBusinessLineId(String tenantCode, String period, String businessLineId) {
        if (StringUtils.isAnyEmpty(tenantCode, period, businessLineId)) {
            return Lists.newArrayList();
        }
        return performanceIndexRepository.queryIndexesByBusinessId(businessLineId, tenantCode, period);
    }

    public String calcIndex(String indexCode, PerformanceUserDO erp, PerformanceProcessResult parentProcessResult, PerformanceTaskItemContext context) {
        String preCalcResult = erp.getFactorValue(erp.getBusinessLineId(), indexCode);
        if (StringUtils.isNotBlank(preCalcResult)) {
            return preCalcResult;
        }
        PerformanceProcessResult processResult = build(erp, indexCode, context);
        String value = calc(indexCode, erp, context);
        addProcess(erp, parentProcessResult, processResult, value);
        return value;
    }


    private String calc(String indexCode, PerformanceUserDO erp, PerformanceTaskItemContext context) {
        String tenantCode = context.getPerformanceTaskItemDO().getTenantCode();
        String period = context.getPerformanceTaskItemDO().getPeriod();
        IndexConstant constant = IndexConstant.getByCode(indexCode);
        if (Objects.isNull(constant)) {
            PerformanceIndex index = context.getPerformanceIndexMap().get(PerformanceIndex.buildKey(tenantCode, period, indexCode));
            if (Objects.isNull(index)) {
                return "0";
            }
            return index.calc(erp, context);
        }
        if (StringUtils.isNotBlank(constant.getLinkTo())) {
            PerformanceIndex index = context.getPerformanceIndexMap().get(PerformanceIndex.buildKey(tenantCode, period, constant.getLinkTo()));
            if (Objects.nonNull(index)) {
                return index.getWeight();
            } else {
                return "0";
            }
        }
        if (IndexConstant.BUSINESS_LINE_CPD.equals(constant)) {
            return StringUtils.isNotBlank(context.getPerformanceTargetDO().getCpd()) ? context.getPerformanceTargetDO().getCpd() : "0";
        }
        if (IndexConstant.BUSINESS_LINE_PRICE.equals(constant)) {
            return StringUtils.isNotBlank(context.getPerformanceTargetDO().getPrice()) ? context.getPerformanceTargetDO().getPrice() : "0";
        }
        if (IndexConstant.BUSINESS_LINE_STANDARD_DAYS.equals(constant)) {
            return StringUtils.isNotBlank(context.getPerformanceTargetDO().getDays()) ? context.getPerformanceTargetDO().getDays() : "0";
        }
        if (IndexConstant.NORMAL_WITHDRAWAL_DURATION.equals(constant)) {
            WaiterHourAdjustment adjustment = context.getWaiterHourAdjustmentMap().get(WaiterHourAdjustment.buildKey(tenantCode, period, "0", erp.getErp()));
            if (Objects.isNull(adjustment)) {
                return "0";
            }
            return String.valueOf(adjustment.getMobilizeHours());
        }
        if (IndexConstant.INSPIRE_WITHDRAWAL_DURATION.equals(constant)) {
            WaiterHourAdjustment adjustment = context.getWaiterHourAdjustmentMap().get(WaiterHourAdjustment.buildKey(tenantCode, period, "1", erp.getErp()));
            if (Objects.isNull(adjustment)) {
                return "0";
            }
            return String.valueOf(adjustment.getMobilizeHours());
        }
        if (IndexConstant.NEW_EMPLOYEE_ACCELERATION_COEFFICIENT.equals(constant)) {
            return calcNewEmployeeAccelerationCoefficient(erp, context);
        }
        return "0";
    }

    private String calcNewEmployeeAccelerationCoefficient(PerformanceUserDO erp, PerformanceTaskItemContext context) {
        return String.valueOf(erp.calculateServiceMonths());
    }

    private void addProcess(PerformanceUserDO erp, PerformanceProcessResult parentProcessResult, PerformanceProcessResult processResult, String result) {
        processResult.setResult(String.valueOf(result));
        //parentProcessResult.getProcessResultList().add(processResult);
        erp.addProcess(processResult);
    }

    private PerformanceProcessResult build(PerformanceUserDO erp,String indexCode, PerformanceTaskItemContext context) {
        PerformanceProcessResult processResult = new PerformanceProcessResult(
                context.getPerformanceTaskItemDO().getTenantCode(),
                context.getPerformanceTaskItemDO().getPeriod(),
                context.getPerformanceTaskItemDO().getBusinessLineId(),
                context.getPerformanceTaskItemDO().getBusinessLineName(),
                context.getPerformanceTaskItemDO().getTaskId(),
                TaskElementTypeEnum.INDEX,
                erp.getErp(),
                indexCode,
                String.format("{%s}", indexCode)
        );
        erp.addProcess(processResult);
        return processResult;
    }
}

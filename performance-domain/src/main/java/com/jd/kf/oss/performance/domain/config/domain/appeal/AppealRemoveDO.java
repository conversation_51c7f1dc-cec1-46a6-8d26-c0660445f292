package com.jd.kf.oss.performance.domain.config.domain.appeal;

import com.jd.kf.oss.performance.domain.config.DomainBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 无效数据剔除领域对象
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AppealRemoveDO extends DomainBaseEntity {

    /**
     * 需要剔除的单号
     */
    private String ticketId;

    /**
     * 绩效月
     */
    private String period;


    /**
     * 指标名称
     */
    private String kpiName;

    /**
     * 保存前校验
     */
    public void validateForSave() {
        if (this.period == null || this.period.trim().isEmpty()) {
            throw new IllegalArgumentException("绩效月不能为空");
        }
        if (this.ticketId == null) {
            throw new IllegalArgumentException("需剔除的业务单号不能为空");
        }
        if (this.kpiName == null || this.kpiName.trim().isEmpty()) {
            throw new IllegalArgumentException("指标名称不能为空");
        }
        // 校验绩效月格式 YYYY-MM
        if (!this.period.matches("\\d{4}-\\d{2}")) {
            throw new IllegalArgumentException("绩效月格式不正确，应为YYYY-MM格式");
        }
    }
}

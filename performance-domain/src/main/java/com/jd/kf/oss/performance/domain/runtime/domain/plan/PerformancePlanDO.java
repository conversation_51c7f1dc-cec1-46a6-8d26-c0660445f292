package com.jd.kf.oss.performance.domain.runtime.domain.plan;

import com.google.common.collect.Maps;
import com.jd.kf.oss.performance.domain.config.domain.formula.FormulaNode;
import com.jd.kf.oss.performance.domain.config.domain.formula.FormulaParameter;
import com.jd.kf.oss.performance.domain.config.domain.formula.FormulaParser;
import com.jd.kf.oss.performance.domain.runtime.RuntimeDomainBaseEntity;
import com.jd.kf.oss.performance.domain.runtime.aggregate.composite.PerformanceTaskItemContext;
import com.jd.kf.oss.performance.domain.runtime.domain.CustomFunctionTypeEnum;
import com.jd.kf.oss.performance.domain.runtime.domain.RoundTypeEnum;
import com.jd.kf.oss.performance.domain.runtime.domain.TaskElementTypeEnum;
import com.jd.kf.oss.performance.domain.runtime.domain.TaskItemStatusEnum;
import com.jd.kf.oss.performance.domain.runtime.domain.customfunction.MatchCustomFunctionService;
import com.jd.kf.oss.performance.domain.runtime.domain.customfunction.RankCustomFunctionService;
import com.jd.kf.oss.performance.domain.runtime.domain.customfunction.SumCustomFunctionService;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceresult.PerformanceProcessResult;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceuser.PerformanceUserDO;
import com.jd.kf.oss.performance.enums.MetaTypeCodeEnum;
import com.jd.kf.oss.performance.utils.SpringUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Data
public class PerformancePlanDO extends RuntimeDomainBaseEntity {
    private static final ScriptEngineManager manager = new ScriptEngineManager();
    private static final ScriptEngine engine = manager.getEngineByName("JavaScript");
    /**
     * 方案名称
     */
    private String code;

    /**
     * 方案名称
     */
    private String name;

    /**
     * 方案类型
     */
    private String type;

    /**
     * 公式
     */
    private String formula;

    /**
     * 保留几位小数
     */
    private Integer decimalPlaces;

    /**
     * 取整类型:0-四舍五入，1-向上取整，2-向下取整
     */
    private RoundTypeEnum roundType;

    /**
     * 绩效月
     */
    private String period;

    /**
     * 因子列表
     */
    Set<PerformanceFactor> factorSet;

    public PerformancePlanDO() {
    }

    /**
     * 构建方案
     * @param tenantCode
     * @param period
     * @param code
     */
    public PerformancePlanDO(String tenantCode, String period, String code) {
        this.setTenantCode(tenantCode);
        this.setCode(code);
        this.period = period;
    }

    /**
     * 查询方案
     * @return
     */
    public PerformancePlanDO load() {
        IPerformancePlanRepository planRepository = SpringUtils.getBean(IPerformancePlanRepository.class);
        return planRepository.queryByCode(this.getTenantCode(), period, this.getCode());
    }

    public String calc(PerformanceUserDO erp, PerformanceTaskItemContext context) throws Exception {
        PerformanceProcessResult processResult = build(erp, context);
        FormulaParser parser = new FormulaParser();
        FormulaNode ast = parser.parse(formula);
        if (Objects.isNull(ast)) {
            return "";
        }
        Map<String, String> paramResultMap = Maps.newHashMap();
        Map<String, PerformanceFactor> paramsMap = Maps.newHashMap();
        for (FormulaParameter param : ast.getParams()) {
            String value = "";
            if (MetaTypeCodeEnum.FACTOR.getCode().equals(param.type)) {
                PerformanceFactor factor = context.getPerformanceFactorMap().get(PerformanceFactor.buildKey(getTenantCode(), getPeriod(), param.getName()));
                value = factor.calc(erp, processResult, context);
                paramsMap.put(param.getOriginText(), factor);
            }
            if (MetaTypeCodeEnum.INDEX.getCode().equals(param.type)) {
                value = Objects.requireNonNull(SpringUtils.getBean(PerformanceIndexService.class)).calcIndex(param.getName(), erp, processResult, context);
            }
            if (MetaTypeCodeEnum.COEFFICIENT.getCode().equals(param.getType())) {
                PerformanceCoefficientDO coefficientDO = context.getCoefficientDOMap().get(PerformanceCoefficientDO.buildKey(getTenantCode(), getPeriod(), param.getName()));
                value = coefficientDO.getCoefficientItems().get(0).getCoefficientNum();
            }
            paramResultMap.put(param.getOriginText(), value);
        }

        for (FormulaNode node : ast.getFunctions()) {
            String value = "";
            if (StringUtils.equalsAnyIgnoreCase(CustomFunctionTypeEnum.MATCH.getCode(), node.getName())) {
                value = Objects.requireNonNull(SpringUtils.getBean(MatchCustomFunctionService.class)).calc(erp, node, processResult, context);
            }
            if (StringUtils.equalsAnyIgnoreCase(CustomFunctionTypeEnum.SUM.getCode(), node.getName())) {
                value = Objects.requireNonNull(SpringUtils.getBean(SumCustomFunctionService.class)).calc(erp, node, processResult, context);
            }
            if (StringUtils.equalsAnyIgnoreCase(CustomFunctionTypeEnum.RANK.getCode(), node.getName())) {
                value = Objects.requireNonNull(SpringUtils.getBean(RankCustomFunctionService.class)).calc(erp, node, processResult, context);
            }
            paramResultMap.put(node.getOriginText(), value);
        }
        String expression = String.valueOf(formula);
        for (Map.Entry<String, String> entry : paramResultMap.entrySet()) {
            String value = entry.getValue();
            if (StringUtils.equalsAnyIgnoreCase(entry.getValue(), "null") || StringUtils.isBlank(entry.getValue())) {
                value = "0";
            }
            expression = expression.replace(entry.getKey(), value);
        }

        if (TaskItemStatusEnum.PLAN_RUNNING.equals(context.getPerformanceTaskItemDO().getStatus())) {
            Object result = "";
            try {
                result = engine.eval(expression);
            } catch (Exception e) {
                log.info("calc error:{}", erp.getErp(), e);
            }
            String value = roundType.processNumber(String.valueOf(result), decimalPlaces);
            addProcess(erp, processResult, true, value, expression, context, paramResultMap, paramsMap);
            return String.valueOf(value);
        } else {
            addProcess(erp, processResult, false, "0", expression, context, paramResultMap, paramsMap);
        }
        return "";
    }

    private static void addProcess(PerformanceUserDO userDO, PerformanceProcessResult processResult, boolean buildDetail,Object result, String expression, PerformanceTaskItemContext context, Map<String, String> paramResultMap, Map<String, PerformanceFactor> paramsMap) {
        String returnStr = String.valueOf(result);
        processResult.setResult(returnStr);
        if (buildDetail) {
            String detail = buildDetail(returnStr, expression, context, paramResultMap, paramsMap);
            processResult.setDetail(detail);
        } else {
            processResult.setDetail(String.format("%s = %s", expression, returnStr));
        }

        userDO.setPerformanceProcessResult(processResult);
        userDO.addProcess(processResult);
    }

    private static String buildDetail(String returnStr, String expression, PerformanceTaskItemContext context, Map<String, String> paramResultMap, Map<String, PerformanceFactor> paramsMap) {
        StringBuilder sb = new StringBuilder();
        String expressionDisplay = String.valueOf(context.getPerformancePlanDO().getFormula());
        for (Map.Entry<String, PerformanceFactor> entry : paramsMap.entrySet()) {
            PerformanceFactor factor = entry.getValue();
            expressionDisplay = expressionDisplay.replace(entry.getKey(), factor.getName());
        }

        sb.append(context.getPerformancePlanDO().getName()).append(":").append(expressionDisplay)
                .append(String.format("\n%s = %s\n", expression, returnStr));
        for (Map.Entry<String, String> entry : paramResultMap.entrySet()) {
            String value = entry.getValue();
            PerformanceFactor factor = paramsMap.get(entry.getKey());
            if (Objects.nonNull(factor)) {
                sb.append("<br>").append(factor.getName()).append(" ").append(" = ").append(value);
            }
        }
        return sb.toString();
    }

    private PerformanceProcessResult build(PerformanceUserDO erp, PerformanceTaskItemContext context) {
        return new PerformanceProcessResult(
                getTenantCode(),
                period,
                context.getPerformanceTaskItemDO().getBusinessLineId(),
                context.getPerformanceTaskItemDO().getBusinessLineName(),
                context.getPerformanceTaskItemDO().getTaskId(),
                TaskElementTypeEnum.PLAN,
                erp.getErp(),
                code,
                formula
        );
    }


}

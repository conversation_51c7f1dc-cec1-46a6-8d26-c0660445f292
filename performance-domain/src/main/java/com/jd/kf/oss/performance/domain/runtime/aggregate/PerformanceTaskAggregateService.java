package com.jd.kf.oss.performance.domain.runtime.aggregate;

import com.google.common.collect.*;
import com.jd.kf.oss.performance.domain.config.domain.businessLine.BusinessLineDO;
import com.jd.kf.oss.performance.domain.config.domain.businessLine.BusinessLineDomainService;
import com.jd.kf.oss.performance.domain.config.domain.performancetarget.PerformanceTargetDO;
import com.jd.kf.oss.performance.domain.config.domain.performancetarget.PerformanceTargetDomainService;
import com.jd.kf.oss.performance.domain.config.domain.plan.PlanDO;
import com.jd.kf.oss.performance.domain.config.domain.plan.PlanDomainService;
import com.jd.kf.oss.performance.domain.runtime.aggregate.composite.PerformanceTaskItemContext;
import com.jd.kf.oss.performance.domain.runtime.domain.TaskElementTypeEnum;
import com.jd.kf.oss.performance.domain.runtime.domain.TaskItemStatusEnum;
import com.jd.kf.oss.performance.domain.runtime.domain.TaskStatusEnum;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceIndexData.IPerformanceIndexDataRepository;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceIndexData.IWaiterHourAdjustmentRepository;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceIndexData.PerformanceIndexData;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceIndexData.WaiterHourAdjustment;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceresult.PerformanceFactorResultDO;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceresult.PerformanceProcessResult;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceresult.PerformanceResultDO;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceresult.PerformanceResultService;
import com.jd.kf.oss.performance.domain.runtime.domain.performancetask.PerformanceTaskDO;
import com.jd.kf.oss.performance.domain.runtime.domain.performancetask.PerformanceTaskItemDO;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceuser.PerformanceUserDO;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceuser.PerformanceUserDomainService;
import com.jd.kf.oss.performance.domain.runtime.domain.plan.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PerformanceTaskAggregateService {
    @Resource
    private PerformanceUserDomainService performanceUserDomainService;

    @Resource
    private PerformanceTargetDomainService performanceTargetDomainService;

    @Resource
    private PlanDomainService planDomainService;

    @Resource
    private IPerformanceIndexDataRepository performanceIndexDataRepository;

    @Resource
    private IWaiterHourAdjustmentRepository waiterHourAdjustmentRepository;

    @Resource
    private PerformanceCoefficientDomainService performanceCoefficientDomainService;

    @Resource
    private PerformanceFactorDomainService performanceFactorDomainService;

    @Resource
    private PerformanceIndexService performanceIndexService;

    @Resource
    private PerformanceResultService performanceResultService;

    @Resource
    private BusinessLineDomainService businessLineDomainService;

    /**
     * 测试
     * @param tenantCode
     * @param period
     * @param formula
     * @param erp
     */
    public PerformanceUserDO calcTest(String tenantCode, String period, String formula, String erp) {
        List<BusinessLineDO> businessLineDOS = queryAllBusinessNameByErp(tenantCode, period, erp);
        if (CollectionUtils.isEmpty(businessLineDOS)) {
            return null;
        }
        PerformanceUserDO  userDO2 = performanceUserDomainService.queryUserByErp(period, erp);
        Map<String, PerformanceProcessResult> factorResultMap = Maps.newHashMap();
        List<CompletableFuture<Void>> futures = businessLineDOS.stream()
                .map(businessLineDO -> CompletableFuture.runAsync(() -> {
                    PerformanceTaskItemDO taskItemDO = buildPerformanceTaskItemDO(tenantCode, period, businessLineDO);
                    PerformanceTaskItemContext context = buildPerformanceTaskItemComposite(taskItemDO);
                    context.getPerformanceTaskItemDO().setStatus(TaskItemStatusEnum.FACTOR_RUNNING);
                    if (CollectionUtils.isNotEmpty(context.getAllUser())) {
                        if (Objects.isNull(context.getPerformancePlanDO())) {
                            return;
                        }
                        Optional<PerformanceUserDO> userDOOptional = context.getAllUser().stream()
                                .filter(a -> StringUtils.equalsAnyIgnoreCase(a.getErp(), erp)).findFirst();
                        PerformanceUserDO userDO = userDOOptional.get();
                        userDO.setFactorResultMap(Maps.newHashMap());
                        context.getPerformancePlanDO().setFormula(formula);
                        try {
                            context.getPerformancePlanDO().calc(userDO, context);
                            synchronized (factorResultMap) {
                                factorResultMap.putAll(userDO.getFactorResultMap());
                            }
                        } catch (Exception e) {
                            log.info("试算第一步错误", e);
                        }
                    }
                }))
                .collect(Collectors.toList());
        futures.forEach(CompletableFuture::join);
        Optional<BusinessLineDO> businessLineDOOptional = businessLineDOS.stream().filter(a -> StringUtils.equalsAnyIgnoreCase(a.getBusinessLineId(), userDO2.getBusinessLineId())).findFirst();
        if (!businessLineDOOptional.isPresent()) {
            return null;
        }
        PerformanceTaskItemDO taskItemDO = buildPerformanceTaskItemDO(tenantCode, period, businessLineDOOptional.get());
        try {
            PerformanceTaskItemContext context = buildPerformanceTaskItemComposite(taskItemDO);
            context.getPerformanceTaskItemDO().setStatus(TaskItemStatusEnum.PLAN_RUNNING);
            Optional<PerformanceUserDO> userDOOptional = context.getAllUser().stream().filter(a -> StringUtils.equalsAnyIgnoreCase(a.getErp(), erp)).findFirst();
            PerformanceUserDO userDO = userDOOptional.get();
            List<PerformanceProcessResult> processResultList = factorResultMap.values().stream().filter(a -> StringUtils.equalsAnyIgnoreCase(a.getStatus(), "已完成")).collect(Collectors.toList());
            for (PerformanceProcessResult processResult : processResultList) {
                userDO.addProcess(processResult);
            }
            context.getPerformancePlanDO().setFormula(formula);
            userDO.setPerformanceProcessResult(null);
            context.getPerformancePlanDO().calc(userDO, context);
            return userDO;
        } catch (Exception e) {
            log.info("试算第二步错误", e);
        }
        return null;
    }

    /**
     * 构建运行子任务
     * @param tenantCode
     * @param period
     * @param businessLineDO
     * @return
     */
    private static PerformanceTaskItemDO buildPerformanceTaskItemDO(String tenantCode, String period, BusinessLineDO businessLineDO) {
        PerformanceTaskItemDO taskItemDO = new PerformanceTaskItemDO();
        taskItemDO.setTenantCode(tenantCode);
        taskItemDO.setPeriod(period);
        taskItemDO.setBusinessLineId(businessLineDO.getBusinessLineId());
        taskItemDO.setBusinessLineName(businessLineDO.getName());
        taskItemDO.setStatus(TaskItemStatusEnum.FACTOR_RUNNING);
        return taskItemDO;
    }

    /**
     * 计算任务
     * @param performanceTaskDO
     */
    public void calc(PerformanceTaskDO performanceTaskDO) {
        if (TaskStatusEnum.NOT_STARTED.equals(performanceTaskDO.getStatus())
                || TaskStatusEnum.FACTOR_RUNNING.equals(performanceTaskDO.getStatus())) {
            performanceTaskDO.changeStatusToFactorRunning();
            performanceTaskDO.getItems().forEach(this::runItemFirst);
            performanceTaskDO.changeStatusToFactorRunningCompleted();
        }
        performanceTaskDO.changeStatusToPlanRunning();
        performanceTaskDO = performanceTaskDO.load(performanceTaskDO.getTenantCode(), performanceTaskDO.getPeriod());
        if (TaskStatusEnum.FACTOR_RUNNING_COMPLETED.equals(performanceTaskDO.getStatus())
                || TaskStatusEnum.PLAN_RUNNING.equals(performanceTaskDO.getStatus())) {
            performanceTaskDO.getItems().forEach(this::runItemSecond);
            performanceTaskDO.changeStatusToPlanCompleted();
        }
    }

    /**
     * 运行第一步
     * @param taskItemDO
     */
    private void runItemFirst(PerformanceTaskItemDO taskItemDO) {
        if (taskItemDO.changeStatusToFactorRunning()) {
            return;
        }
        PerformanceTaskItemContext context = buildPerformanceTaskItemComposite(taskItemDO);
        List<PerformanceUserDO> userDOList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(context.getUserDOList())) {
            userDOList.addAll(context.getUserDOList());
        }
        if (Objects.isNull(context.getPerformancePlanDO())) {
            return;
        }
        if (CollectionUtils.isNotEmpty(context.getAssistUserDOList())) {
            userDOList.addAll(context.getAssistUserDOList());
        }
        userDOList = userDOList.stream().filter(a -> a.getPlanDO() != null).collect(Collectors.toList());
        for (PerformanceUserDO userDO : userDOList) {
            try {
                context.getPerformancePlanDO().calc(userDO, context);
            } catch (Exception e) {
                log.info("calc error:{}", userDO.getErp(), e);
            }
        }
        context.getPerformanceTaskItemDO().changeStatusToFactorRunningCompleted();
        saveFactorProcess(context);
    }

    private void runItemSecond(PerformanceTaskItemDO taskItemDO) {
        if (taskItemDO.changeStatusToPlanRunning()) {
            return;
        }
        PerformanceTaskItemContext context = buildPerformanceTaskItemComposite(taskItemDO);
        List<PerformanceUserDO> userDOList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(context.getUserDOList())) {
            userDOList.addAll(context.getUserDOList());
        }
        if (Objects.isNull(context.getPerformancePlanDO())) {
            return;
        }
        userDOList = userDOList.stream().filter(a -> a.getPlanDO() != null).collect(Collectors.toList());
        for (PerformanceUserDO userDO : userDOList) {
            try {
                context.getPerformancePlanDO().calc(userDO, context);
            } catch (Exception e) {
                log.info("计算报错", e);
            }
        }
        context.getPerformanceTaskItemDO().changeStatusToPlanCompleted();
        savePlanProcess(context);
        saveFactorProcess(context);
    }

    private void savePlanProcess(PerformanceTaskItemContext context) {
        List<PerformanceProcessResult> factorResult = Lists.newArrayList();
        context.getUserDOList().forEach(a -> {
            factorResult.add(a.getPerformanceProcessResult());
        });

        List<PerformanceResultDO> resultDOS = context.getUserDOList().stream().map(PerformanceTaskAggregateService::convertToResult).collect(Collectors.toList());
        performanceResultService.batchSavePlan(resultDOS);
    }

    private void saveFactorProcess(PerformanceTaskItemContext context) {
        List<PerformanceProcessResult> factorResult = Lists.newArrayList();
        List<PerformanceUserDO> userDOS = context.getAllUser();
        userDOS.forEach(a -> {
            if (CollectionUtils.isNotEmpty(a.getFactorResultMap().values())) {
                List<PerformanceProcessResult> tmpResults = Lists.newArrayList(a.getFactorResultMap().values());
                tmpResults = tmpResults.stream().filter(b -> TaskElementTypeEnum.FACTOR.equals(b.getElementTypeEnum())).collect(Collectors.toList());
                tmpResults = tmpResults.stream().filter(b -> StringUtils.equalsAnyIgnoreCase(b.getStatus(), "已完成")).collect(Collectors.toList());
                factorResult.addAll(tmpResults);
            }
        });
        List<PerformanceFactorResultDO> resultDOS = factorResult.stream().map(PerformanceTaskAggregateService::convertToFactorResultDO).collect(Collectors.toList());
        Map<String, PerformanceFactorResultDO> resultDOMap = Maps.newHashMap();
        for (PerformanceFactorResultDO resultDO : resultDOS) {
            resultDOMap.put(resultDO.buildKey(), resultDO);
        }
        performanceResultService.batchSave(Lists.newArrayList(resultDOMap.values()));
    }

    public static PerformanceFactorResultDO convertToFactorResultDO(PerformanceProcessResult processResult) {
        if (processResult == null) {
            return null;
        }

        PerformanceFactorResultDO resultDO = new PerformanceFactorResultDO();
        // 复制相同属性
        BeanUtils.copyProperties(processResult, resultDO);
        // 设置特殊属性
        resultDO.setFactorId(processResult.getCode()); // code映射为factorId
        resultDO.setType("首次"); // 默认类型为首次

        return resultDO;
    }

    private static PerformanceProcessResult convertToFactorResultDO(PerformanceFactorResultDO resultDO) {
        if (resultDO == null) {
            return null;
        }

        PerformanceProcessResult processResult = new PerformanceProcessResult();
        // 复制相同属性
        BeanUtils.copyProperties(resultDO, processResult);
        // 设置特殊属性
        processResult.setCode(resultDO.getFactorId()); // code映射为factorId
        processResult.setElementTypeEnum(TaskElementTypeEnum.FACTOR);
        return processResult;
    }



    private static PerformanceResultDO convertToResult(PerformanceUserDO performanceUserDO) {
        if (performanceUserDO == null || performanceUserDO.getPerformanceProcessResult() == null) {
            return null;
        }

        PerformanceResultDO processResult = new PerformanceResultDO();
        // 复制相同属性
        BeanUtils.copyProperties(performanceUserDO.getPerformanceProcessResult(), processResult);
        // 设置特殊属性
        processResult.setStatus("首次"); // 默认类型为首次
        return processResult;
    }


    public void getAllProcessResult(List<PerformanceProcessResult> factorProcess ,PerformanceProcessResult performanceProcessResult) {
        if (Objects.isNull(performanceProcessResult)) {
            return;
        }
        if (TaskElementTypeEnum.FACTOR.equals(performanceProcessResult.getElementTypeEnum())) {
            factorProcess.add(performanceProcessResult);
        }
        if (CollectionUtils.isNotEmpty(performanceProcessResult.getProcessResultList())) {
            for (PerformanceProcessResult processResult : performanceProcessResult.getProcessResultList()) {
                getAllProcessResult(factorProcess, processResult);
            }
        }
    }

    /**
     * 构建 PerformanceTaskItemComposite
     * @param taskItemDO
     * @return
     */
    private PerformanceTaskItemContext buildPerformanceTaskItemComposite(PerformanceTaskItemDO taskItemDO) {
        PerformanceTaskItemContext composite = new PerformanceTaskItemContext();
        composite.setPerformanceTaskItemDO(taskItemDO);
        buildTarget(taskItemDO, composite);
        buildPlan(composite);
        if (Objects.isNull(composite.getPerformancePlanDO())) {
            return composite;
        }

        buildCoefficient(taskItemDO, composite);
        buildFactor(taskItemDO, composite);
        buildIndex(taskItemDO, composite);
        buildData(taskItemDO, composite);
        buildAllTargetDO(taskItemDO, composite);
        buildUser(taskItemDO, composite);
        buildAssistUser(taskItemDO, composite);
        buildHourData(taskItemDO, composite);
        return composite;
    }

    private void buildUser(PerformanceTaskItemDO taskItemDO, PerformanceTaskItemContext composite) {
        List<PerformanceUserDO> userDOS = queryUser(taskItemDO.getTenantCode(), taskItemDO.getBusinessLineId(), taskItemDO.getPeriod(), taskItemDO.getTaskId());
        composite.setUserDOList(userDOS);
    }

    private void buildTarget(PerformanceTaskItemDO taskItemDO, PerformanceTaskItemContext composite) {
        PerformanceTargetDO performanceTargetDO = performanceTargetDomainService.queryTargetByBusinessLineIdAndPeriod(taskItemDO.getBusinessLineId(), taskItemDO.getPeriod());
        composite.setPerformanceTargetDO(performanceTargetDO);
    }

    private void buildFactor(PerformanceTaskItemDO taskItemDO, PerformanceTaskItemContext composite) {
        List<PerformanceFactor> factorList = performanceFactorDomainService.loadAll(taskItemDO.getTenantCode(), taskItemDO.getPeriod());
        Map<String, PerformanceFactor> factorMap = factorList.stream().collect(Collectors.toMap(PerformanceFactor::buildKey, PerformanceFactor->PerformanceFactor, (a, b) -> a));
        composite.setPerformanceFactorMap(factorMap);
    }

    private void buildCoefficient(PerformanceTaskItemDO taskItemDO, PerformanceTaskItemContext composite) {
        List<PerformanceCoefficientDO> coefficientDOS = performanceCoefficientDomainService.loadAll(taskItemDO.getTenantCode(), taskItemDO.getPeriod());
        Map<String, PerformanceCoefficientDO> coefficientDOMap = coefficientDOS.stream().collect(Collectors.toMap(PerformanceCoefficientDO::buildKey, PerformanceCoefficientDO->PerformanceCoefficientDO, (a, b) -> a));
        composite.setCoefficientDOMap(coefficientDOMap);
    }

    private static void buildPlan(PerformanceTaskItemContext composite) {
        PerformanceTargetDO performanceTargetDO = composite.getPerformanceTargetDO();
        if (Objects.isNull(performanceTargetDO)) {
            return;
        }
        PerformancePlanDO planDO = new PerformancePlanDO(performanceTargetDO.getTenantCode(), performanceTargetDO.getPeriod(), performanceTargetDO.getEvaluationPlanCode());
        planDO = planDO.load();
        composite.setPerformancePlanDO(planDO);
    }

    private void buildData(PerformanceTaskItemDO taskItemDO, PerformanceTaskItemContext composite) {
        //List<PerformanceIndexData> indexDataDOS = performanceIndexDataRepository.query(taskItemDO.getTenantCode(), taskItemDO.getPeriod(), taskItemDO.getBusinessLineName());
        List<PerformanceIndexData> indexDataDOS = performanceIndexDataRepository.query(taskItemDO.getTenantCode(), taskItemDO.getPeriod(), composite.getPerformanceTargetDO().getBusinessLineName());
        Map<String, PerformanceIndexData> indexDataDOMap = indexDataDOS.stream().collect(Collectors.toMap(PerformanceIndexData::buildKey, PerformanceIndexDataDO-> PerformanceIndexDataDO, (a,b) -> a));
        composite.setIndexDataDOMap(indexDataDOMap);
    }

    private List<BusinessLineDO> queryAllBusinessNameByErp(String tenantCode, String period, String erp) {
        if (StringUtils.isAnyEmpty(tenantCode, period, erp)) {
            return Lists.newArrayList();
        }
        List<String> businessNames = performanceIndexDataRepository.queryAllBusinessNames(tenantCode, period, erp);
        List<BusinessLineDO> businessLineDOS = businessLineDomainService.queryBusinessLineByNames(businessNames);
        if (CollectionUtils.isEmpty(businessLineDOS)) {
            return Lists.newArrayList();
        }
        return businessLineDOS;
    }

    private void buildHourData(PerformanceTaskItemDO taskItemDO, PerformanceTaskItemContext composite) {
        if (CollectionUtils.isEmpty(composite.getUserDOList())) {
            return;
        }
        List<String> erps = composite.getUserDOList().stream().map(PerformanceUserDO::getErp).collect(Collectors.toList());
        List<WaiterHourAdjustment> adjustments = waiterHourAdjustmentRepository.query(taskItemDO.getTenantCode(), taskItemDO.getPeriod(), erps);
        Map<String, WaiterHourAdjustment> waiterHourAdjustmentMap = adjustments.stream().collect(Collectors.toMap(WaiterHourAdjustment::buildKey, WaiterHourAdjustment-> WaiterHourAdjustment));
        composite.setWaiterHourAdjustmentMap(waiterHourAdjustmentMap);
    }

    private void buildIndex(PerformanceTaskItemDO taskItemDO, PerformanceTaskItemContext composite) {
        List<PerformanceIndex> indexList = performanceIndexService.queryByBusinessLineId(taskItemDO.getTenantCode(), taskItemDO.getPeriod(), taskItemDO.getBusinessLineId());
        Map<String, PerformanceIndex> performanceIndexMap = indexList.stream().collect(Collectors.toMap(PerformanceIndex::buildKey, PerformanceIndex-> PerformanceIndex, (a, b) -> a));
        composite.setPerformanceIndexMap(performanceIndexMap);
    }

    private void buildAssistUser(PerformanceTaskItemDO taskItemDO, PerformanceTaskItemContext composite) {
        Set<String> assistErps = composite.getUserDOList().stream().map(PerformanceUserDO::getErp).collect(Collectors.toSet());
        Set<String> erps = composite.getIndexDataDOMap().values().stream().
                filter(a -> !assistErps.contains(a.getErp())).
                map(PerformanceIndexData::getErp).
                collect(Collectors.toSet());
        List<PerformanceUserDO> performanceUserDOS = performanceUserDomainService.queryUserByErps(taskItemDO.getPeriod(), Lists.newArrayList(erps));
        buildPerformanceTargetDO(taskItemDO.getTenantCode(), taskItemDO.getPeriod(), performanceUserDOS);
        buildPlan(taskItemDO.getTenantCode(), taskItemDO.getPeriod(), performanceUserDOS);
        composite.setAssistUserDOList(performanceUserDOS);
    }

    /**
     * 查询用户
     * @param businessLineId
     * @param period
     * @return
     */
    private List<PerformanceUserDO> queryUser(String tenantCode, String businessLineId, String period, Long taskId) {
        List<PerformanceUserDO> performanceUserDOS = performanceUserDomainService.queryUserByBusinessLineId(businessLineId, period);
        buildPerformanceTargetDO(tenantCode, period, performanceUserDOS);
        buildPlan(tenantCode, period, performanceUserDOS);
        buildFactorResult(tenantCode, period, taskId, performanceUserDOS);
        return performanceUserDOS;
    }

    private void buildFactorResult(String tenantCode, String period, Long taskId, List<PerformanceUserDO> performanceUserDOS) {
        List<String> erps = performanceUserDOS.stream().map(PerformanceUserDO::getErp).collect(Collectors.toList());
        List<PerformanceFactorResultDO> resultDOS = performanceResultService.query(tenantCode, period, taskId, erps);
        Multimap<String, PerformanceFactorResultDO> resultDOMultimap = ArrayListMultimap.create();
        for (PerformanceFactorResultDO resultDO : resultDOS) {
            resultDOMultimap.put(resultDO.getErp(), resultDO);
        }
        performanceUserDOS.forEach(a -> {
            if (CollectionUtils.isNotEmpty(resultDOMultimap.get(a.getErp()))) {
                resultDOMultimap.get(a.getErp()).forEach(
                        b -> {
                            PerformanceProcessResult result = convertToFactorResultDO(b);
                            a.getFactorResultMap().put(result.buildKey(), result);
                        }
                );
            }
        });
    }

    private void buildAllTargetDO(PerformanceTaskItemDO taskItemDO, PerformanceTaskItemContext composite) {
        List<PerformanceTargetDO> targetDOS = performanceTargetDomainService.queryAllTargetByPeriod(taskItemDO.getPeriod());
        if (CollectionUtils.isEmpty(targetDOS)) {
            return;
        }
        //Set<String> businessNameSets = composite.getIndexDataDOMap().values().stream().map(PerformanceIndexData::getBusinessJxName).collect(Collectors.toSet());
        //targetDOS = targetDOS.stream().filter(a -> businessNameSets.contains(a.getBusinessLineName())).collect(Collectors.toList());
        Map<String, PerformanceTargetDO> targetDOMap = targetDOS.stream().collect(Collectors.toMap(PerformanceTargetDO::getBusinessLineId, PerformanceTargetDO->PerformanceTargetDO, (a, b) -> a));
        composite.setTargetDOMap(targetDOMap);
    }

    /**
     * 构建绩效目标
     * @param period
     * @param performanceUserDOS
     */
    private void buildPerformanceTargetDO(String tenantCode, String period, List<PerformanceUserDO> performanceUserDOS) {
        if (CollectionUtils.isEmpty(performanceUserDOS)) {
            return;
        }
        List<PerformanceTargetDO> targetDOS = performanceTargetDomainService.queryAllTargetByPeriod(period);
        if (CollectionUtils.isEmpty(targetDOS)) {
            return;
        }
        Map<String, PerformanceTargetDO> targetDOMap = targetDOS.stream().collect(Collectors.toMap(PerformanceTargetDO::getBusinessLineId, PerformanceTargetDO->PerformanceTargetDO, (a, b) -> a));
        for (PerformanceUserDO performanceUserDO : performanceUserDOS) {
            if (targetDOMap.get(performanceUserDO.getBusinessLineId()) != null) {
                performanceUserDO.setPerformanceTargetDO(targetDOMap.get(performanceUserDO.getBusinessLineId()));
            }
        }
    }

    private void buildPlan(String tenantCode, String period, List<PerformanceUserDO> performanceUserDOS) {
        List<String> planCodes =Lists.newArrayList();
        for (PerformanceUserDO performanceUserDO : performanceUserDOS) {
            if (Objects.nonNull(performanceUserDO) && Objects.nonNull(performanceUserDO.getPerformanceTargetDO())) {
                planCodes.add(performanceUserDO.getPerformanceTargetDO().getEvaluationPlanCode());
            }
        }
        if (CollectionUtils.isEmpty(planCodes)) {
            return;
        }
        List<PlanDO> planDOS = planDomainService.queryPlanByCodes(tenantCode, period, planCodes);
        Map<String, PlanDO> planDOMap = planDOS.stream().collect(Collectors.toMap(PlanDO::getCode, PlanDO->PlanDO, (a, b) -> a));

        for (PerformanceUserDO performanceUserDO : performanceUserDOS) {
            if (Objects.isNull(performanceUserDO.getPerformanceTargetDO())) {
                continue;
            }
            if (planDOMap.get(performanceUserDO.getPerformanceTargetDO().getEvaluationPlanCode()) != null) {
                performanceUserDO.setPlanDO(planDOMap.get(performanceUserDO.getPerformanceTargetDO().getEvaluationPlanCode()));
            }
        }
    }

}

package com.jd.kf.oss.performance.domain.runtime.domain.performanceIndexData;

import java.util.List;

public interface IPerformanceIndexDataRepository {

    /**
     * 查询绩效条线下的所有数据
     * @param tenantCode
     * @param period
     * @param businessLineName
     * @return
     */
    List<PerformanceIndexData> query(String tenantCode, String period, String businessLineName);

    /**
     * 查询所有业务线名称
     * @param tenantCode
     * @param period
     * @param erp
     * @return
     */
    List<String> queryAllBusinessNames(String tenantCode, String period, String erp);
}

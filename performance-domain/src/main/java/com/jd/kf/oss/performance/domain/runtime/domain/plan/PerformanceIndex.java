package com.jd.kf.oss.performance.domain.runtime.domain.plan;

import com.jd.kf.oss.performance.domain.runtime.RuntimeDomainBaseEntity;
import com.jd.kf.oss.performance.domain.runtime.aggregate.composite.PerformanceTaskItemContext;
import com.jd.kf.oss.performance.domain.runtime.domain.customfunction.MatchCustomFunctionService;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceIndexData.PerformanceIndexData;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceuser.PerformanceUserDO;
import com.jd.kf.oss.performance.enums.IndexConstant;
import com.jd.kf.oss.performance.enums.IndexStatusEnum;
import com.jd.kf.oss.performance.enums.IndexTemplate;
import com.jd.kf.oss.performance.utils.SpringUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Data
public class PerformanceIndex extends RuntimeDomainBaseEntity {

    /**
     * 条线ID
     */
    private String businessLineId;

    /**
     * 指标code
     */
    private String kpiCd;

    /**
     * 指标名称
     */
    private String kpiName;

    /**
     * kpi 类型: type =1 数量指标, type=2  率指标
     */
    private Integer kpiType;

    /**
     * 指标映射code
     * 如<质量指标1,index_quality_1>
     */
    private IndexTemplate template;

    /**
     * 描述
     */
    private String description;

    /**
     * 指标平台url跳转id
     */
    private String indexPlatformUrlCode;

    /**
     * 指标平台url跳转url
     */
    private String indexPlatformUrl;

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 结束时间
     */
    private String endDate;

    /**
     * 样本下限
     */
    private String threshold;

    /**
     * 状态
     */
    private IndexStatusEnum status;

    /**
     * 权重
     */
    private String weight;

    /**
     * 绩效月
     */
    private String period;

    public String buildKey() {
        String key = "index_tenantCode:%s_period:%s_code:%s";
        return String.format(key, getTenantCode(), period, template.getCode());
    }

    public static String buildKey(String tenantCode, String period, String template) {
        String key = "index_tenantCode:%s_period:%s_code:%s";
        return String.format(key, tenantCode, period, template);
    }

    /**
     * 计算index
     * @param erp
     * @param context
     * @return
     */
    public String calc(PerformanceUserDO erp, PerformanceTaskItemContext context) {
        PerformanceIndexData indexDataDO = context.getIndexDataDOMap().get(buildKey(erp.getErp()));
        String value = "";
        if (Objects.isNull(indexDataDO)) {
            return value;
        }
        if (this.getKpiType() == 1) {
            value = indexDataDO.getNumerator();
        }

        if (this.getKpiType() == 2) {
            Double denominator = Double.valueOf(indexDataDO.getDenominator());
            Double numerator = Double.valueOf(indexDataDO.getNumerator());
            if (Math.abs(denominator) > 1e-9) {
                value = String.valueOf(numerator / denominator);
            }
        }

        if (StringUtils.isBlank(threshold) || StringUtils.isBlank(value)) {
            return value;
        }

        Double th = Double.valueOf(threshold);
        Double va = Double.valueOf(value);

        return th < va ? value : "0";
    }

    public String buildKey(String erp) {
        String key = "IndexData_tenantCode:%s_period:%s_code:%s_erp:%s";
        return String.format(key, getTenantCode(), period, kpiCd, erp);
    }
}

package com.jd.kf.oss.performance.domain.runtime.domain.performancetask;

import com.jd.kf.oss.performance.domain.config.domain.businessLine.BusinessLineDO;
import com.jd.kf.oss.performance.domain.runtime.RuntimeDomainBaseEntity;
import com.jd.kf.oss.performance.domain.runtime.domain.TaskItemStatusEnum;
import com.jd.kf.oss.performance.domain.runtime.domain.TaskStatusEnum;
import com.jd.kf.oss.performance.utils.SpringUtils;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 绩效任务领域模型
 */
@Data
public class PerformanceTaskDO extends RuntimeDomainBaseEntity {
    /**
     * 绩效周期(格式:yyyyMM)
     */
    private String period;

    /**
     * 任务状态
     */
    private TaskStatusEnum status;

    /**
     * 子任务项列表
     */
    private List<PerformanceTaskItemDO> items;

    /**
     * 创建绩效任务
     */
    public PerformanceTaskDO create(String tenantCode, String period,
                                           List<BusinessLineDO> businessLines) {
        IPerformanceTaskRepository repository = SpringUtils.getBean(IPerformanceTaskRepository.class);
        // 取消同周期未开始的任务
        repository.cancelUnstartedTasks(tenantCode, period);

        PerformanceTaskDO task = new PerformanceTaskDO();
        task.setTenantCode(tenantCode);
        task.setPeriod(period);
        task.setStatus(TaskStatusEnum.NOT_STARTED);
        task.setItems(createTaskItems(tenantCode, period, businessLines, task.getId()));
        repository.save(task);
        return task;
    }

    private static List<PerformanceTaskItemDO> createTaskItems(String tenantCode,
                                                               String period, List<BusinessLineDO> businessLines, Long taskId) {
        return businessLines.stream()
                .map(b -> PerformanceTaskItemDO.build(tenantCode, period, b, taskId))
                .collect(Collectors.toList());
    }

    public PerformanceTaskDO load(String tenantCode, String period) {
        IPerformanceTaskRepository repository = SpringUtils.getBean(IPerformanceTaskRepository.class);
        return repository.query(tenantCode, period);
    }

    /**
     * 校验因子是否运行结束
     * @param tenantCode
     * @param period
     * @return
     */
    public boolean checkAllItemStatus(String tenantCode, String period, TaskItemStatusEnum taskItemStatusEnum) {
        IPerformanceTaskRepository repository = SpringUtils.getBean(IPerformanceTaskRepository.class);
        PerformanceTaskDO taskDO = repository.query(tenantCode, period);
        List<PerformanceTaskItemDO> taskItemDOS = taskDO.getItems().stream()
                .filter(a -> !taskItemStatusEnum.equals(a.getStatus()))
                .collect(Collectors.toList());
        return CollectionUtils.isEmpty(taskItemDOS);
    }


    /**
     * 将状态修改为因子运行
     * @return
     */
    public boolean changeStatusToFactorRunning() {
        if (!TaskStatusEnum.NOT_STARTED.equals(getStatus())) {
            return false;
        }
        return Objects.requireNonNull(SpringUtils.getBean(IPerformanceTaskRepository.class))
                .changeStatus(this, TaskStatusEnum.NOT_STARTED, TaskStatusEnum.FACTOR_RUNNING);
    }

    /**
     * 将状态修改为因子运行结束
     * @return
     */
    public boolean changeStatusToFactorRunningCompleted() {
        if (!TaskStatusEnum.FACTOR_RUNNING.equals(getStatus())) {
            return false;
        }
        if (!checkAllItemStatus(getTenantCode(), getPeriod(), TaskItemStatusEnum.FACTOR_RUNNING_COMPLETED)) {
            return false;
        }
        return Objects.requireNonNull(SpringUtils.getBean(IPerformanceTaskRepository.class))
                .changeStatus(this, TaskStatusEnum.FACTOR_RUNNING, TaskStatusEnum.FACTOR_RUNNING_COMPLETED);
    }

    /**
     * 将状态修改为方案运行开始
     * @return
     */
    public boolean changeStatusToPlanRunning() {
        if (!TaskStatusEnum.FACTOR_RUNNING_COMPLETED.equals(getStatus())) {
            return false;
        }
        return Objects.requireNonNull(SpringUtils.getBean(IPerformanceTaskRepository.class))
                .changeStatus(this, TaskStatusEnum.FACTOR_RUNNING_COMPLETED, TaskStatusEnum.PLAN_RUNNING);
    }

    /**
     * 将状态修改为方案运行结束
     * @return
     */
    public boolean changeStatusToPlanCompleted() {
        if (!TaskStatusEnum.PLAN_RUNNING.equals(getStatus())) {
            return false;
        }
        if (!checkAllItemStatus(getTenantCode(), getPeriod(), TaskItemStatusEnum.COMPLETED)) {
            return false;
        }
        return Objects.requireNonNull(SpringUtils.getBean(IPerformanceTaskRepository.class))
                .changeStatus(this, TaskStatusEnum.PLAN_RUNNING, TaskStatusEnum.COMPLETED);
    }
}
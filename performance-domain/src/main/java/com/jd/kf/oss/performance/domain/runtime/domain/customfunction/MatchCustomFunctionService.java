package com.jd.kf.oss.performance.domain.runtime.domain.customfunction;

import com.jd.kf.oss.performance.domain.config.domain.formula.FormulaNode;
import com.jd.kf.oss.performance.domain.config.domain.formula.FormulaParameter;
import com.jd.kf.oss.performance.domain.runtime.aggregate.composite.PerformanceTaskItemContext;
import com.jd.kf.oss.performance.domain.runtime.domain.CustomFunctionTypeEnum;
import com.jd.kf.oss.performance.domain.runtime.domain.TaskElementTypeEnum;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceresult.PerformanceProcessResult;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceuser.PerformanceUserDO;
import com.jd.kf.oss.performance.domain.runtime.domain.plan.*;
import com.jd.kf.oss.performance.enums.CoefficientTemplateTypeEnum;
import com.jd.kf.oss.performance.enums.MetaTypeCodeEnum;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
public class MatchCustomFunctionService {
    @Resource
    private RankCustomFunctionService rankCustomFunctionService;
    @Autowired
    @Lazy
    private SumCustomFunctionService sumCustomFunctionService;

    @Resource
    private PerformanceIndexService performanceIndexService;

    public String calc(PerformanceUserDO erp, FormulaNode formulaNode, PerformanceProcessResult parentProcessResult, PerformanceTaskItemContext context) throws Exception {
        String preCalcResult = erp.getFactorValue(erp.getBusinessLineId(), formulaNode.originText);
        if (StringUtils.isNotBlank(preCalcResult)) {
            return preCalcResult;
        }
        PerformanceProcessResult processResult = build(erp, formulaNode.getOriginText(), formulaNode.originText, context);
        String tenantCode = context.getPerformanceTaskItemDO().getTenantCode();
        String period = context.getPerformanceTargetDO().getPeriod();
        PerformanceCoefficientDO coefficientDO = null;
        String value = "";
        for (FormulaParameter param : formulaNode.getParams()) {
            if (MetaTypeCodeEnum.COEFFICIENT.getCode().equals(param.type)) {
                coefficientDO = context.getCoefficientDOMap().get(PerformanceCoefficientDO.buildKey(tenantCode, period, param.getName()));
            }
            if (MetaTypeCodeEnum.INDEX.getCode().equals(param.type)) {
                value = performanceIndexService.calcIndex(param.getName(), erp, processResult, context);
            }
            if (MetaTypeCodeEnum.FACTOR.getCode().equals(param.type)) {
                PerformanceFactor factor = context.getPerformanceFactorMap().get(PerformanceFactor.buildKey(tenantCode, period, param.getName()));
                value = factor.calc(erp, processResult, context);
            }
        }

        for (FormulaNode node : formulaNode.getFunctions()) {
            if (StringUtils.equalsAnyIgnoreCase(CustomFunctionTypeEnum.MATCH.getCode(), node.getName())) {
                value = calc(erp, node, processResult, context);
            }
            if (StringUtils.equalsAnyIgnoreCase(CustomFunctionTypeEnum.SUM.getCode(), node.getName())) {
                value = sumCustomFunctionService.calc(erp, node, processResult, context);
            }
            if (StringUtils.equalsAnyIgnoreCase(CustomFunctionTypeEnum.RANK.getCode(), node.getName())) {
                value = rankCustomFunctionService.calc(erp, node, processResult, context);
            }
        }
        PerformanceCoefficientItem result = match(coefficientDO, value);
        addProcess(erp,parentProcessResult, processResult, result, value);
        return Objects.isNull(result) || Objects.isNull(result.getCoefficientNum()) ? null : result.getCoefficientNum();
    }

    private PerformanceCoefficientItem match(PerformanceCoefficientDO coefficientDO, String value) {
        if (Objects.isNull(coefficientDO) || StringUtils.isBlank(value)) {
            return null;
        }
        if (CoefficientTemplateTypeEnum.CONSTANT.equals(coefficientDO.getType())) {
            return coefficientDO.getCoefficientItems().get(0);
        }

        List<PerformanceCoefficientItem>  items = coefficientDO.getCoefficientItems();
        sortByLeftEndpointLambda(items);

        PerformanceCoefficientItem result = null;
        if (coefficientDO.getType().equals(CoefficientTemplateTypeEnum.MONTHLY)) {
            result = matchItem(value, items);
        }
        if (coefficientDO.getType().equals(CoefficientTemplateTypeEnum.SEGMENT)) {
            result = matchItemRange(value, items);
        }
        return result;
    }

    private static PerformanceCoefficientItem matchItemRange(String value, List<PerformanceCoefficientItem> items) {
        PerformanceCoefficientItem result = null;
        Double target = Double.valueOf(value);
        for (PerformanceCoefficientItem item : items) {
            Double left = getValue(item.getLeftEndpoint());
            Double right = getValue(item.getRightEndpoint());
            if (left <= target && target <= right) {
                result = item;
                break;
            }
        }
        return Objects.nonNull(result) ? result : items.get(items.size() - 1);
    }

    private static PerformanceCoefficientItem matchItem(String value, List<PerformanceCoefficientItem> items) {
        PerformanceCoefficientItem result = null;
        Double target = Double.valueOf(value);
        for (PerformanceCoefficientItem item : items) {
            Double left = getValue(item.getLeftEndpoint());
            if (left <= target) {
                result = item;
            } else {
                break;
            }
        }
        return Objects.nonNull(result) ? result : items.get(items.size() - 1);
    }

    private static Double getValue(String item) {
        return item == null || item.isEmpty() ? null : Double.parseDouble(item);
    }

    /**
     * 排序
     * @param items
     */
    public static void sortByLeftEndpointLambda(List<PerformanceCoefficientItem> items) {
        items.sort((o1, o2) -> {
            String left1 = o1.getLeftEndpoint();
            String left2 = o2.getLeftEndpoint();

            if (left1 == null || left1.isEmpty()) {
                return (left2 == null || left2.isEmpty()) ? 0 : -1;
            }
            if (left2 == null || left2.isEmpty()) {
                return 1;
            }
            try {
                return Double.compare(
                        Double.parseDouble(left1),
                        Double.parseDouble(left2)
                );
            } catch (NumberFormatException e) {
                return left1.compareTo(left2);
            }
        });
    }

    private void addProcess(PerformanceUserDO erp, PerformanceProcessResult parentProcessResult, PerformanceProcessResult processResult, PerformanceCoefficientItem result, String value) {
        StringBuilder sb = new StringBuilder();
        sb.append(processResult.getFormula());
        if (Objects.nonNull(result)) {
            sb.append(String.format("\ncurrentValue:%s matchItem left:%s ~ right:%s itemValue:%s\n", value, result.getLeftEndpoint(), result.getRightEndpoint(), result.getCoefficientNum()));

        } else {
            sb.append(String.format("\ncurrentValue:%s mot matched", value));
        }
        processResult.setResult(Objects.nonNull(result) ? result.getCoefficientNum() : null);
        processResult.setDetail(sb.toString());
        processResult.setStatus("已完成");
        //parentProcessResult.getProcessResultList().add(processResult);
        erp.addProcess(processResult);
    }

    private PerformanceProcessResult build(PerformanceUserDO erp, String code, String formula, PerformanceTaskItemContext context) {
        return new PerformanceProcessResult(
                context.getPerformanceTaskItemDO().getTenantCode(),
                context.getPerformanceTaskItemDO().getPeriod(),
                context.getPerformanceTaskItemDO().getBusinessLineId(),
                context.getPerformanceTaskItemDO().getBusinessLineName(),
                context.getPerformanceTaskItemDO().getTaskId(),
                TaskElementTypeEnum.FACTOR,
                erp.getErp(),
                code,
                formula
        );
    }
}

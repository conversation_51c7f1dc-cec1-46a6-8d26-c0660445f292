package com.jd.kf.oss.performance.domain.config.aggregate.configuration;

import com.google.common.collect.Lists;
import com.jd.dal.common.utils.CollectionUtils;
import com.jd.dal.common.utils.StringUtils;
import com.jd.kf.oss.performance.context.UserContextHolder;
import com.jd.kf.oss.performance.domain.config.aggregate.configuration.composite.BusinessLinePlanComposite;
import com.jd.kf.oss.performance.domain.config.domain.businessLine.BusinessLineDomainService;
import com.jd.kf.oss.performance.domain.config.domain.coefficient.CoefficientDO;
import com.jd.kf.oss.performance.domain.config.domain.coefficient.CoefficientDomainService;
import com.jd.kf.oss.performance.domain.config.domain.factor.FactorDO;
import com.jd.kf.oss.performance.domain.config.domain.factor.FactorDomainService;
import com.jd.kf.oss.performance.domain.config.domain.formula.FormulaNode;
import com.jd.kf.oss.performance.domain.config.domain.formula.FormulaParameter;
import com.jd.kf.oss.performance.domain.config.domain.formula.FormulaParser;
import com.jd.kf.oss.performance.domain.config.domain.index.IndexDO;
import com.jd.kf.oss.performance.domain.config.domain.index.IndexDomainService;
import com.jd.kf.oss.performance.domain.config.domain.performancetarget.PerformanceTargetDO;
import com.jd.kf.oss.performance.domain.config.domain.performancetarget.PerformanceTargetDomainService;
import com.jd.kf.oss.performance.domain.config.domain.plan.PlanDO;
import com.jd.kf.oss.performance.domain.config.domain.plan.PlanDomainService;
import com.jd.kf.oss.performance.exception.BizException;
import com.jd.kf.oss.performance.utils.CheckUtil;
import com.jd.kf.oss.performance.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PlanAggregateService {
    @Resource
    private PlanDomainService planDomainService;

    @Resource
    private FactorDomainService factorDomainService;

    @Resource
    private IndexDomainService indexDomainService;

    @Resource
    private PerformanceTargetDomainService performanceTargetDomainService;


    @Resource
    private BusinessLineDomainService businessLineDomainService;

    /**
     * 校验是否成环
     * @return
     */
    public boolean checkCircle(PlanDO planDO) throws Exception{
        List<String> factors = planDO.getFactorCodes();
        return factorDomainService.checkCircle(planDO.getTenantCode(), planDO.getPeriod(), factors);
    }

//    private BusinessLinePlanComposite buildPlanComposite(PerformanceTargetDO targetDO, PlanDO planDO, List<FactorDO> factorDOS, List<CoefficientDO> coefficientDOS, List<IndexDO> indexDOS) {
//        BusinessLinePlanComposite planComposite = new BusinessLinePlanComposite();
//        planComposite.setTargetDO(targetDO);
//        planComposite.setPlanDO(planDO);
//        analysisFormula(planDO.getFormula(), planComposite, factorDOS, coefficientDOS, indexDOS);
//        List<IndexDO> indexes = matchIndexes(targetDO.getBusinessLineId(), planComposite.getIndexTemplateNames(), indexDOS);
//        planComposite.setIndexDOS(indexes);
//        return planComposite;
//    }

//    private void analysisFormula(String formula, BusinessLinePlanComposite planComposite, List<FactorDO> factorDOS, List<CoefficientDO> coefficientDOS, List<IndexDO> indexDOS) {
//        FormulaNode ast = null;
//        try {
//            FormulaParser parser = new FormulaParser();
//            ast = parser.parse(formula);
//        } catch (Exception e) {
//        }
//
//        if (Objects.nonNull(ast)) {
//            List<FormulaParameter> indexParameters= ast.getAllIndex();
//            if (CollectionUtils.isNotEmpty(indexParameters)) {
//                planComposite.getIndexTemplateNames().addAll(indexParameters.stream().map(FormulaParameter::getName).collect(Collectors.toList()));
//            }
//
//            List<FormulaParameter> coefficientParameters = ast.getAllCoefficient();
//            if (CollectionUtils.isNotEmpty(coefficientParameters)) {
//                planComposite.getCoefficientCodes().addAll(coefficientParameters.stream().map(FormulaParameter::getName).collect(Collectors.toList()));
//                planComposite.getCoefficientDOS().addAll(matchCoefficient(ast.getAllCoefficient(), coefficientDOS));
//            }
//
//            List<FormulaParameter> factorParameters = ast.getAllFactor();
//            if (CollectionUtils.isNotEmpty(coefficientParameters)) {
//                planComposite.getFactorCodes().addAll(factorParameters.stream().map(FormulaParameter::getName).collect(Collectors.toList()));
//                List<FactorDO> factors = matchFactor(ast.getAllFactor(), factorDOS);
//                planComposite.getFactorDOS().addAll(factors);
//                if (CollectionUtils.isNotEmpty(factors)) {
//                    for (FactorDO factorDO : factors) {
//                        analysisFormula(factorDO.getFormula(), planComposite, factorDOS, coefficientDOS, indexDOS);
//                    }
//                }
//            }
//        }
//    }

//    /**
//     * 匹配指标
//     * @param parameters
//     * @param factorDOS
//     * @return
//     */
//    private List<FactorDO> matchFactor(List<FormulaParameter> parameters, List<FactorDO> factorDOS) {
//        if (CollectionUtils.isEmpty(parameters)) {
//            return Lists.newArrayList();
//        }
//
//        Set<String> sets = parameters.stream().map(FormulaParameter::getName).collect(Collectors.toSet());
//        return factorDOS.stream().filter(a -> sets.contains(a.getCode())).collect(Collectors.toList());
//    }

//    /**
//     * 匹配指标
//     * @param businessLineId
//     * @param indexTemplateCodes
//     * @param indexDOS
//     * @return
//     */
//    private List<IndexDO> matchIndexes(String businessLineId, List<String> indexTemplateCodes, List<IndexDO> indexDOS) {
//        if (CollectionUtils.isEmpty(indexTemplateCodes) || CollectionUtils.isEmpty(indexDOS)) {
//            return Lists.newArrayList();
//        }
//        return indexDOS.stream().filter(a ->
//                StringUtils.equalsIgnoreCase(a.getBusinessLineId(), businessLineId) && indexTemplateCodes.contains(a.getTemplate().getCode()))
//                .collect(Collectors.toList());
//    }
//
//    /**
//     * 匹配指标
//     * @param parameters
//     * @param coefficientDOS
//     * @return
//     */
//    private List<CoefficientDO> matchCoefficient(List<FormulaParameter> parameters, List<CoefficientDO> coefficientDOS) {
//        if (CollectionUtils.isEmpty(parameters)) {
//            return Lists.newArrayList();
//        }
//
//        Set<String> sets = parameters.stream().map(FormulaParameter::getName).collect(Collectors.toSet());
//        return coefficientDOS.stream().filter(a -> sets.contains(a.getCode())).collect(Collectors.toList());
//    }


    /**
     * 检查绩效因子是否被任何绩效方案引用
     * @param factorCode
     * @param period
     * @return
     */
    public boolean weatherFactorUsed(String factorCode, String period) {
        CheckUtil.notBlank(factorCode, period, "因子编码和周期不能为空");
        String tenantCode = UserContextHolder.getTenantCode();
        FactorDO factorDO = new FactorDO(UserContextHolder.getTenantCode(), factorCode, period);
        factorDO = factorDO.loadByCode();
        List<PlanDO> planDOS = planDomainService.loadAllPlan(tenantCode, period);
        if( CollectionUtils.isEmpty(planDOS)) {
            return false;
        }
        List<FactorDO> factorDOS = factorDomainService.loadAllFactors(tenantCode, period);
        // 一旦匹配到相等的系数code，则返回true
        Boolean result =  Optional.ofNullable(planDOS)
                .orElse(Collections.emptyList())
                .parallelStream()
                .map(PlanDO::getFormula)
                .filter(StringUtils::isNotBlank)
                .flatMap(formula -> Optional.ofNullable(PlanDomainService.parseFormulaFactor(formula, factorDOS))
                        .orElse(Collections.emptySet())
                        .stream())
                .anyMatch(factorCode::equals);

        // 一旦匹配到相等的系数code，则返回true
        boolean result2 = FactorDomainService.parseFormula(factorDO.getFormula(), factorDOS, FormulaNode::getAllFactor).stream().anyMatch(factorCode::equals);
        return  result2 || result;
    }

    /**
     * //TODO  优化只查询plan和coefficient
     * 检查绩效系数是否被任何绩效方案引用
     * @param coefficientCode
     * @param period
     * @return
     */
    public boolean isCoefficientUsedByPlanOrFactor(String coefficientCode, String period) {
        CheckUtil.notBlank(coefficientCode, period, "系数编码和周期不能为空");
        String tenantCode = UserContextHolder.getTenantCode();
        List<PlanDO> planDOS = planDomainService.loadAllPlan(tenantCode, period);
        List<FactorDO> factorDOS = factorDomainService.loadAllFactors(tenantCode, period);
        if( CollectionUtils.isEmpty(planDOS) && CollectionUtils.isEmpty(factorDOS)) {
            return false;
        }
        // 获取所有的公式
        List<String> allFormulas = getAllFormulas(planDOS, factorDOS);
//        Map<String,Set<String>> m=new HashMap<>();
//        for(String formula:allFormulas){
//            Set<String> collect=PlanDomainService.parseFormulaCoefficient(formula, factorDOS);
//            m.put(formula,collect);
//        }
        // 一旦匹配到相等的系数code，则返回true
      return  allFormulas.parallelStream()
                .flatMap(formula -> Optional.ofNullable(PlanDomainService.parseFormulaCoefficient(formula, factorDOS))
                        .orElse(Collections.emptySet())
                        .stream())
                .anyMatch(coefficientCode::equals);
    }

    private List<String> getAllFormulas(List<PlanDO> planDOS, List<FactorDO> factorDOS) {
        List<String> allFormulas =new ArrayList<>();
        List<String> formulasOfPlan=Optional.ofNullable(planDOS).orElse(Collections.emptyList())
                .stream().map(PlanDO::getFormula).collect(Collectors.toList());
        List<String> formulasOfFactor=Optional.of(factorDOS).orElse(Collections.emptyList())
                .stream().map(FactorDO::getFormula).collect(Collectors.toList());
        allFormulas.addAll(formulasOfPlan);
        allFormulas.addAll(formulasOfFactor);
        return  allFormulas;
    }
    /**
     * 关联绩效组
     */
    @Transactional(rollbackFor = Exception.class)
    public void associateBusinessLines(List<String> businessLineIds,String period, String planCode) {
        preCheckForAssociate(period, planCode, businessLineIds);
        String tenantCode = UserContextHolder.getTenantCode();
        // 校验方案是否存在，存在则返回方案
        PlanDO  planDo=planDomainService.checkAndGetEvaluationPlan(planCode, period);
        // 绩效组去重
        businessLineIds = businessLineIds.stream().distinct().collect(Collectors.toList());
        // 校验绩效组是否都有效
        businessLineDomainService.checkAllBusinessLineExits(businessLineIds);
        // 关联绩效组，返回需要创建index的绩效组ids（过滤一些已经关联了该方案的绩效组）
        List<String> lineIds=performanceTargetDomainService.filterTargetAndAssociatePlan(businessLineIds,period,planCode);
        // 为每个绩效组关联的绩效方案创建对应的指标
        createIndexesForPlan(tenantCode, period, planDo, lineIds);
    }



    private void createIndexesForPlan(String tenantCode, String period, PlanDO planDo, List<String> businessLineIds) {
       if(CollectionUtils.isEmpty(businessLineIds)){
           //全部都是重复关联的，则条线为空
           return;
       }
        // 获取所有的因子，方便解析方案中的因子字符串
        List<FactorDO> factorDOS = factorDomainService.loadAllFactors(tenantCode, period);
        // 获取去重后的指标模板code
        Set<String> indexTemplateCodeOfPlan=planDomainService.parseFormulaIndexTemplates(planDo.getFormula(),factorDOS);
        if(CollectionUtils.isEmpty(indexTemplateCodeOfPlan)){
            return;
        }
        // 根据方案中的指标模板code创建对应的绩效组的指标
        indexDomainService.associatePlanIndexesToBusinessLines(indexTemplateCodeOfPlan,businessLineIds,period);
    }


    /**
     * 取消关联绩效组
     */
    @Transactional(rollbackFor = Exception.class)
    public void disassociateBusinessLines(List<String> businessLineIds,String period, String planCode) {
        preCheckForAssociate(period, planCode, businessLineIds);
        // 绩效组去重
        businessLineIds = businessLineIds.stream().distinct().collect(Collectors.toList());
        // 获取绩效组对应的绩效目标
        List<PerformanceTargetDO> targetInDB =
                performanceTargetDomainService.queryTargetListByBusinessLineCodeAndPeriod(period, businessLineIds);
        // 校验绩效组是否关联了该绩效方案，绩效组id是否有效
        checkTargetWhetherAssociatedPlan(planCode, targetInDB, businessLineIds);
        List<Long> targetIds= targetInDB.stream()
                .map(PerformanceTargetDO::getId)
                .collect(Collectors.toList());
        // 批量更新绩效目标的方案编码为null
        performanceTargetDomainService.updateTargetPlanCode(targetIds,null);
        // 批量删除绩效组的指标
        indexDomainService.deleteBatchByBusinessLineIdsAndPeriod(businessLineIds,period);
    }

    /**
     * 校验取消关联的绩效组是否都是有效的
     * 校验绩效组是否关联了该绩效方案
     */
    private void checkTargetWhetherAssociatedPlan(String planCode, List<PerformanceTargetDO> targetInDB, List<String> uniBusinessLineIds) {
        if(targetInDB == null || targetInDB.isEmpty() || targetInDB.size()!=uniBusinessLineIds.size()){
            log.error("取消关联失败，数据库中绩效目标数量与绩效组数量不一致，" +
                    "可能存在无效绩效组ID或者绩效组没有创建方案,businessLineIds:{}",uniBusinessLineIds);
            throw new IllegalArgumentException("存在无效绩效组ID或者部分绩效组还没有创建绩效方案");
        }
        // 校验是否每个绩效组的绩效目标都关联了该方案
        List<String> linesUnAssociatedPlanCode=targetInDB.stream()
                .filter(targetDO -> !Objects.equals(targetDO.getEvaluationPlanCode(), planCode))
                .map(PerformanceTargetDO::getBusinessLineId)
                .collect(Collectors.toList());
        // 如果存在绩效组并未关联该方案，则抛出异常
        if(CollectionUtils.isNotEmpty(linesUnAssociatedPlanCode)){
            log.error("取消关联失败，存在绩效组并未关联该方案,businessLineIds:{}",linesUnAssociatedPlanCode);
            throw new IllegalArgumentException(String.format("存在绩效组 [%s] 未关联该方案",
                    String.join(", ", linesUnAssociatedPlanCode)));
        }
    }

    private void preCheckForAssociate(String period, String planCode, List<String> businessLineIds) {
        if(CollectionUtils.isEmpty(businessLineIds)){
            throw new BizException("绩效组不能为空");
        }
        CheckUtil.notBlank(period, planCode, "绩效月和绩效方案编码不能为空");
    }
    public Map<String,Set<String>> queryTemplateCodeOfPlan(List<String> businessLineIds, String period) {
        if (CollectionUtils.isEmpty(businessLineIds)) {
            return new HashMap<>(0);
        }
        String tenantCode = UserContextHolder.getTenantCode();
        List<PerformanceTargetDO> performanceTargetDOS = performanceTargetDomainService.queryTargetListByBusinessLineCodeAndPeriod(period, businessLineIds);
        List<PlanDO> planDOS = planDomainService.loadAllPlan(tenantCode, period);
        if( CollectionUtils.isEmpty(planDOS)) {
            return new HashMap<>(0);
        }
        List<FactorDO> factorDOS = factorDomainService.loadAllFactors(tenantCode, period);
        Map<String,Set<String>> templateCodeOfPlan=new HashMap<>(planDOS.size());
        for(PlanDO planDO:planDOS){
            if(StringUtils.isBlank(planDO.getFormula())){
                continue;
            }
            Set<String> indexTemplateNames=planDomainService.parseFormulaIndexTemplates(planDO.getFormula(),factorDOS);
            templateCodeOfPlan.put(planDO.getCode(),indexTemplateNames);
        }
        Map<String,Set<String>> lineAndIndexTemplateMap = new HashMap<>(businessLineIds.size());
        performanceTargetDOS.forEach(targetDO->{
            String businessId=targetDO.getBusinessLineId();
            Set<String> indexTemplateNames=templateCodeOfPlan.get(targetDO.getEvaluationPlanCode());
            lineAndIndexTemplateMap.put(businessId,indexTemplateNames);
        });
        return lineAndIndexTemplateMap;
    }


    public Set<String> analysisIndexTemplateNames(String formula){
        String period= DateUtils.getCurrentPerformancePeriod();
        String tenantCode = UserContextHolder.getTenantCode();
        // 获取所有的因子，方便解析方案中的因子字符串
        List<FactorDO> factorDOS = factorDomainService.loadAllFactors(tenantCode, period);
        // 获取去重后的指标模板code
        return planDomainService.parseFormulaIndexTemplates(formula,factorDOS);
    }


}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.kf.oss.performance.infra.mybatis.mapper.PerformanceTargetPOMapper">

    <!-- 联表查询绩效目标信息，直接返回TargetDO -->
    <select id="queryTargetWithJoin" resultType="com.jd.kf.oss.performance.domain.config.domain.performancetarget.PerformanceTargetDO">
        SELECT
            pt.id,
            pt.tenant_code as tenantCode,
            pt.business_line_id as businessLineId,
            pt.evaluation_plan_code as evaluationPlanCode,
            pt.period,
            pt.price,
            pt.cpd,
            pt.days,
            pt.code,
            pt.creator,
            pt.editor,
            pt.created,
            pt.modified,
            bl.name as businessLineName,
            p.name as evaluationPlan,
            p.type as evaluationType
        FROM performance_target pt
        LEFT JOIN business_line bl ON pt.business_line_id = bl.business_line_id AND bl.yn = 1
        LEFT JOIN plan p ON pt.evaluation_plan_code = p.code AND p.yn = 1 and pt.period=p.period
        WHERE pt.yn = 1
        <if test="tenantCode != null and tenantCode != ''">
            AND pt.tenant_code = #{tenantCode}
        </if>
        <if test="period != null and period != ''">
            AND pt.period = #{period}
        </if>
        <if test="businessLineId != null and businessLineId != ''">
            AND pt.business_line_id = #{businessLineId}
        </if>
        <if test="planCode != null and planCode != ''">
            AND pt.evaluation_plan_code = #{planCode}
        </if>
        <if test="businessLineName != null and businessLineName != ''">
            AND bl.name LIKE CONCAT('%', #{businessLineName}, '%')
        </if>
        ORDER BY pt.modified DESC, pt.created DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <!-- 统计绩效目标总数（联表查询） -->
    <select id="countTargetWithJoin" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT pt.id)
        FROM performance_target pt
        LEFT JOIN business_line bl ON pt.business_line_id = bl.business_line_id AND bl.yn = 1
        LEFT JOIN plan p ON pt.evaluation_plan_code = p.code AND p.yn = 1 and pt.period = p.period
        WHERE pt.yn = 1
        <if test="tenantCode != null and tenantCode != ''">
            AND pt.tenant_code = #{tenantCode}
        </if>
        <if test="period != null and period != ''">
            AND pt.period = #{period}
        </if>
        <if test="businessLineId != null and businessLineId != ''">
            AND pt.business_line_id = #{businessLineId}
        </if>
        <if test="planCode != null and planCode != ''">
            AND pt.evaluation_plan_code = #{planCode}
        </if>
        <if test="businessLineName != null and businessLineName != ''">
            AND bl.name LIKE CONCAT('%', #{businessLineName}, '%')
        </if>
    </select>

    <!-- 根据业务线ID、租户标识和绩效月查询单个绩效目标详情（联表查询） -->
    <select id="queryTargetDetailByBusinessLineId" resultType="com.jd.kf.oss.performance.domain.config.domain.performancetarget.PerformanceTargetDO">
        SELECT
            pt.id,
            pt.tenant_code as tenantCode,
            pt.business_line_id as businessLineId,
            pt.evaluation_plan_code as evaluationPlanCode,
            pt.period,
            pt.price,
            pt.cpd,
            pt.days,
            pt.code,
            pt.creator,
            pt.editor,
            pt.created,
            pt.modified,
            bl.name as businessLineName,
            bl.channel as channel,
            p.name as evaluationPlan,
            p.type as evaluationType
        FROM performance_target pt
        LEFT JOIN business_line bl ON pt.business_line_id = bl.business_line_id AND bl.yn = 1
        LEFT JOIN plan p ON pt.evaluation_plan_code = p.code AND p.yn = 1 and pt.period=p.period
        WHERE pt.yn = 1
        AND pt.business_line_id = #{businessLineId}
        AND pt.tenant_code = #{tenantCode}
        AND pt.period = #{period}
        LIMIT 1
    </select>

    <!-- 根据业务线ID列表和绩效月批量查询绩效目标（联表查询） -->
    <select id="queryTargetListWithJoin" resultType="com.jd.kf.oss.performance.domain.config.domain.performancetarget.PerformanceTargetDO">
        SELECT
            pt.id,
            pt.tenant_code as tenantCode,
            pt.business_line_id as businessLineId,
            pt.evaluation_plan_code as evaluationPlanCode,
            pt.period,
            pt.price,
            pt.cpd,
            pt.days,
            pt.code,
            pt.creator,
            pt.editor,
            pt.created,
            pt.modified,
            bl.name as businessLineName,
            p.name as evaluationPlan,
            p.type as evaluationType
        FROM performance_target pt
        LEFT JOIN business_line bl ON pt.business_line_id = bl.business_line_id AND bl.yn = 1
        LEFT JOIN plan p ON pt.evaluation_plan_code = p.code AND p.yn = 1 AND pt.period=p.period
        WHERE pt.yn = 1
        AND pt.business_line_id IN
        <foreach collection="businessLineIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND pt.period = #{period}
        ORDER BY pt.modified DESC
    </select>

</mapper>

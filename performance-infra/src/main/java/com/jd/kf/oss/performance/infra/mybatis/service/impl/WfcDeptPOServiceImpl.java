package com.jd.kf.oss.performance.infra.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.jd.kf.oss.performance.constants.SystemConstants;
import com.jd.kf.oss.performance.context.UserContextHolder;
import com.jd.kf.oss.performance.infra.mybatis.entity.WfcDeptPO;
import com.jd.kf.oss.performance.infra.mybatis.mapper.WfcDeptPOMapper;
import com.jd.kf.oss.performance.infra.mybatis.service.IWfcDeptPOService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 部门表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
@Service
public class WfcDeptPOServiceImpl extends ServiceImpl<WfcDeptPOMapper, WfcDeptPO> implements IWfcDeptPOService {

    private static final int BATCH_SIZE = 100;

    @Resource
    private WfcDeptPOMapper wfcDeptPOMapper;

    @Override
    public void batchUpsertDept(List<WfcDeptPO> wfcDeptPOList) {
        if (CollectionUtils.isEmpty(wfcDeptPOList)) {
            return;
        }

        Lists.partition(wfcDeptPOList, BATCH_SIZE).forEach(wfcDeptPOs -> {
            wfcDeptPOMapper.batchSaveOrUpdate(wfcDeptPOs);
        });
    }

    @Override
    public void batchRemoveDeptById(List<WfcDeptPO> wfcDeptPOList) {
        if (CollectionUtils.isEmpty(wfcDeptPOList)) {
            return;
        }
        List<Long> idList = wfcDeptPOList.stream()
                .map(WfcDeptPO::getId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        wfcDeptPOMapper.batchDeleteByIdList(idList);
    }

    @Override
    public List<WfcDeptPO> getWfcDeptListByDeptIds(List<String> deptIds) {
        String tenantCode = UserContextHolder.getTenantCode();
        if (CollectionUtils.isEmpty(deptIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<WfcDeptPO> queryWrapper = Wrappers.<WfcDeptPO>lambdaQuery()
                .in(WfcDeptPO::getDeptId, deptIds)
                .eq(WfcDeptPO::getTenantCode, tenantCode)
                .eq(WfcDeptPO::getYn, SystemConstants.VALID);

        return this.getBaseMapper().selectList(queryWrapper);
    }

    @Override
    public List<String> querySubDeptIdsByParentDeptId(String tenantCode, String parentDeptId) {
        if (parentDeptId == null || parentDeptId.trim().isEmpty()) {
            return Collections.emptyList();
        }
        if (tenantCode == null || tenantCode.trim().isEmpty()) {
            return Collections.emptyList();
        }
        return wfcDeptPOMapper.querySubDeptIdsByParentDeptId(tenantCode, parentDeptId);
    }
}

package com.jd.kf.oss.performance.infra.config;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Sets;
import com.jd.laf.binding.annotation.JsonConverter;
import com.jd.laf.config.spring.annotation.LafValue;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2025/4/1 23:26
 * @Description:
 * @Version 1.0
 */

@Slf4j
@Data
@Component
public class DynamicConfig {

    /**
     * 统一运营门户获取租户列表，需要获取哪些资源类型的
     */
    @LafValue("kf.performance.portal.tenant.resource.type.set")
    @JsonConverter
    private Set<Integer> portalTenantResourceType = Sets.newHashSet( 4);

    /**
     * 拦截器校验开关
     */
    @LafValue("kf.performance.admin.auth")
    private boolean adminAuth = true;

    @LafValue("kf.performance.index.platform.url.prefix")
    private String indexPlatformUrlPrefix = "http://one-service.jd.com/#/indicator/detail?index=";

    @LafValue("kf.performance.tenant.code.map")
    private Map<String, String> tenantCodeMap = ImmutableMap.of(
            "kf_wfc_retail", "kf_perform_retail"
    );

    @LafValue("kf.performance.tenant.code.channel.list")
    private Map<String, List<String>> tenantCode2ChannelMap = new HashMap<String, List<String>>() {{
        put("kf_perform_retail", Arrays.asList(
                "咨询-电话", "咨询-在线", "售后", "仲裁", "商服-在线", "商服-电话",
                "人机异步", "CRC即时-在线", "CRC即时-电话", "二线", "CRC迟滞-自营", "CRC迟滞-POP"
        ));
    }};

    /**
     * 批量导出
     */
    @LafValue("kf.performance.export.filename.prefix")
    private String exportPerformanceBatchDataFileNamePrefix = "绩效系统";


    @LafValue("kf.performance.export.excel.max.row.count")
    private Integer exportExcelMaxRowCount = 1000;

    @LafValue("kf.performance.import.excel.max.row.count")
    private Integer importExcelMaxRowCount = 1000;

    @LafValue("jss.hostName")
    private String hostName="storage.jd.local";

    @LafValue("jss.connectionTimeout")
    private Integer connectionTimeout=30000;

    @LafValue("jss.credential.accessKeyId.ddfile")
    private String ddFileAccessKeyId="rNTo3WeCgiX8gBnf";

    @LafValue("jss.credential.secretAccessKeyId.ddfile")
    private String ddFileSecretAccessKeyId="grhS6usUq1beuFFIf6cUO7vBO3q9nBJQIUy07HLI";

    @LafValue("kf.performance.cpd.excel.template")
    private String cpdExcelTemplate="http://storage.jd.local/im.roster/%E7%BB%A9%E6%95%88%E7%B3%BB%E7%BB%9F_175376922266936f5_%E5%AE%A2%E6%9C%8D%E7%BB%A9%E6%95%88_%E7%9B%AE%E6%A0%87CPD%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx?Expires=4909442822&AccessKey=rNTo3WeCgiX8gBnf&Signature=YqpqnHXgfRuDMxLeQHzzbsXbIL8%3D";

    @LafValue("kf.performance.price.and.days.excel.template")
    private String priceAndDaysExcelTemplate= "http://storage.jd.local/im.roster/%E7%BB%A9%E6%95%88%E7%B3%BB%E7%BB%9F_1753769181480df12_%E5%AE%A2%E6%9C%8D%E7%BB%A9%E6%95%88_%E5%8D%95%E4%BB%B7_%E6%9C%88%E6%A0%87%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx?Expires=4909442781&AccessKey=rNTo3WeCgiX8gBnf&Signature=zEEsd3StelQLD6QbAW3%2F52y5kZU%3D";

    @LafValue("kf.performance.target.index.excel.template")
    private String targetIndexExcelTemplate="http://storage.jd.local/im.roster/%E7%BB%A9%E6%95%88%E7%B3%BB%E7%BB%9F_1753769117997ee78_%E5%AE%A2%E6%9C%8D%E7%BB%A9%E6%95%88_%E8%80%83%E6%A0%B8%E6%96%B9%E6%A1%88%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx?Expires=4909442718&AccessKey=rNTo3WeCgiX8gBnf&Signature=r4cCsi6XhpsMAOrNfGfk3C204Yg%3D";

    @LafValue("kf.performance.target.evaluate.excel.template")
    private String evaluateExcelTemplate="http://storage.jd.local/im.roster/%E7%BB%A9%E6%95%88%E7%B3%BB%E7%BB%9F_1753769431068fa68_%E4%B8%BB%E7%AE%A1%E8%AF%84%E4%BB%B7%E5%AF%BC%E5%85%A5%E6%A8%A1%E7%89%88.xlsx?Expires=4909443031&AccessKey=rNTo3WeCgiX8gBnf&Signature=cETfXEHajOq65DsIDtwfVHPwQJc%3D";

    @LafValue("kf.performance.target.appeal.remove.excel.template")
    private String appealRemoveExcelTemplate="http://storage.jd.local/im.roster/%E7%BB%A9%E6%95%88%E7%B3%BB%E7%BB%9F_175376945670583a8_%E6%97%A0%E6%95%88%E6%95%B0%E6%8D%AE%E5%89%94%E9%99%A4%E5%AF%BC%E5%85%A5%E6%A8%A1%E7%89%88.xlsx?Expires=4909443056&AccessKey=rNTo3WeCgiX8gBnf&Signature=q0WzXtMmXqwRwI%2FgPBID7grHF38%3D";

    @LafValue("kf.performance.target.appeal.modify.excel.template")
    private String appealModifyExcelTemplate="http://storage.jd.local/im.roster/%E7%BB%A9%E6%95%88%E7%B3%BB%E7%BB%9F_17537693840632657_%E5%BD%92%E5%B1%9E%E6%95%B0%E6%8D%AE%E4%BF%AE%E6%94%B9%E5%AF%BC%E5%85%A5%E6%A8%A1%E7%89%88.xlsx?Expires=4909442984&AccessKey=rNTo3WeCgiX8gBnf&Signature=fjkmK7rKvm7S0NVQgH6NR7aQNVg%3D";

    @LafValue("kf.performance.export.appeal.modify.limit")
    private int exportAppealModifyLimit = 2000;


    @LafValue("kf.performance.plan.associate.businessLine.max.amount")
    private int planAssociateBusinessLineMaxAmount = 1000;


    @LafValue("kf.performance.test")
    private String duccTest;
}

package com.jd.kf.oss.performance.infra.runtime.performancetask;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceIndexData.IPerformanceIndexDataRepository;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceIndexData.PerformanceIndexData;
import com.jd.kf.oss.performance.infra.mybatis.entity.IndexDataPO;
import com.jd.kf.oss.performance.infra.mybatis.service.IIndexDataPOService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class PerformanceIndexDataRepository implements IPerformanceIndexDataRepository {
    @Resource
    private IIndexDataPOService indexDataPOService;

    @Override
    public List<PerformanceIndexData> query(String tenantCode, String period, String businessLineName) {
        Long gtId = 0L;
        Integer limit = 100;
        List<PerformanceIndexData> results = Lists.newArrayList();
        for (int i = 0 ; i<= 100; i++) {
            List<PerformanceIndexData> result = query(tenantCode, period, businessLineName, gtId, limit);
            if (CollectionUtils.isNotEmpty(result)) {
                gtId = result.get(result.size() - 1).getId();
                results.addAll(result);
            } else {
                break;
            }
        }
        return results;
    }

    //@Override
    public List<PerformanceIndexData> query(String tenantCode, String period, String businessLineName, Long gtId, Integer limit) {
        List<IndexDataPO> indexDataPOS = indexDataPOService.lambdaQuery()
                .eq(IndexDataPO::getTenantCode, tenantCode)
                .eq(IndexDataPO::getPeriod, period)
                .eq(IndexDataPO::getBusinessJxName, businessLineName)
                .gt(IndexDataPO::getId, gtId)
                .orderByAsc(IndexDataPO::getId)
                .last("limit " + limit)
                .list();
        if (CollectionUtils.isEmpty(indexDataPOS)) {
            return Lists.newArrayList();
        }
        return PerformanceIndexDataConverter.INSTANCE.poList2DOList(indexDataPOS);
    }

    /**
     *
     * @param tenantCode
     * @param period
     * @param erp
     * @return
     */
    public List<String> queryAllBusinessNames(String tenantCode, String period, String erp) {
        QueryWrapper<IndexDataPO> wrapper = new QueryWrapper<>();
        wrapper.select("DISTINCT business_jx_name")
                .eq("tenant_code", tenantCode)
                .eq("period", period)
                .eq("erp", erp);
        List<IndexDataPO> indexDataPOS = indexDataPOService.list(wrapper);
        if (CollectionUtils.isEmpty(indexDataPOS)) {
            return Lists.newArrayList();
        }
        return indexDataPOS.stream().map(IndexDataPO::getBusinessJxName).distinct().collect(Collectors.toList());
    }
}

package com.jd.kf.oss.performance.infra.configuration.appealmodify.converter;

import com.jd.kf.oss.performance.domain.config.domain.appeal.AppealModifyDO;
import com.jd.kf.oss.performance.infra.mybatis.entity.AppealModifyPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 归属数据修改PO转换器
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Mapper
public interface AppealModifyPOConverter {
    
    AppealModifyPOConverter INSTANCE = Mappers.getMapper(AppealModifyPOConverter.class);

    /**
     * DO转PO
     */
    AppealModifyPO do2PO(AppealModifyDO appealModifyDO);

    /**
     * PO转DO
     */
    AppealModifyDO po2DO(AppealModifyPO appealModifyPO);
}

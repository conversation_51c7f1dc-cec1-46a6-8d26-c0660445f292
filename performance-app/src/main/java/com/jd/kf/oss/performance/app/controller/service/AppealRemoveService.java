package com.jd.kf.oss.performance.app.controller.service;

import com.alibaba.excel.EasyExcel;
import com.jd.kf.oss.performance.app.controller.converter.AppealRemoveConverter;
import com.jd.kf.oss.performance.app.controller.dto.request.appeal.ExportAppealRemoveRequest;
import com.jd.kf.oss.performance.app.controller.dto.vo.ApiResult;
import com.jd.kf.oss.performance.app.controller.dto.vo.appeal.AppealRemoveExportVO;
import com.jd.kf.oss.performance.app.controller.dto.vo.appeal.AppealRemoveImportVO;
import com.jd.kf.oss.performance.domain.config.domain.appeal.AppealRemoveDO;
import com.jd.kf.oss.performance.domain.config.domain.appeal.AppealRemoveDomainService;
import com.jd.kf.oss.performance.infra.config.DynamicConfig;
import com.jd.kf.oss.performance.domain.config.aggregate.common.ExcelCheckContext;
import com.jd.kf.oss.performance.app.controller.utils.EasyExcelBatchImportListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * 无效数据剔除Service
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@Service
public class AppealRemoveService {

    @Autowired
    private AppealRemoveDomainService appealRemoveDomainService;

    @Autowired
    private DynamicConfig dynamicConfig;

    @Autowired
    private FileHelperService fileHelperService;

    /**
     * 导出无效数据剔除
     * @param request 导出请求参数
     * @param httpServletRequest HTTP请求
     * @return OSS文件URL
     */
    public String exportAppealRemove(ExportAppealRemoveRequest request, HttpServletRequest httpServletRequest) {
        // 查询数据
        List<AppealRemoveDO> appealRemoveList = appealRemoveDomainService.queryAppealRemoveList(
                request.getPeriod(), null, null);
        // 校验导出数量限制
        fileHelperService.checkExportAmountLimit(appealRemoveList);
        // 转换为导出VO
        List<AppealRemoveExportVO> exportVOList = AppealRemoveConverter.INSTANCE.doList2ExportVOList(appealRemoveList);
        // 上传OSS并返回URL
        String fileName = "无效数据剔除导出-" + request.getPeriod();
        return fileHelperService.uploadToOSS(httpServletRequest, fileName, AppealRemoveExportVO.class, exportVOList);
    }

    /**
     * 导入无效数据剔除
     * @param url Excel文件链接
     * @param httpServletRequest HTTP请求
     * @return 导入结果
     */
    public ApiResult<String> importAppealRemove(String url, HttpServletRequest httpServletRequest) throws IOException {
        File excelFile = fileHelperService.downloadFile(url);
        fileHelperService.checkExcelFileRowCount(excelFile);
        Consumer<ExcelCheckContext<AppealRemoveDO>> checkFunction = this::checkImportData;
        EasyExcelBatchImportListener<AppealRemoveDO, AppealRemoveImportVO> importHelper = getImportListener(checkFunction);
        EasyExcel.read(excelFile, importHelper).sheet().doRead();
        if (com.jd.dal.common.utils.CollectionUtils.isEmpty(importHelper.getErrorMessages())) {
            return ApiResult.success();
        }
        String failedUrl = fileHelperService.uploadToOSS(httpServletRequest, "无效数据剔除导入失败条目", importHelper.buildErrorSheet());
        return ApiResult.error(failedUrl);
    }

    private void checkImportData(ExcelCheckContext<AppealRemoveDO> context) {
        Consumer<AppealRemoveDO> checkFunction = AppealRemoveDO::validateForSave;
        checkRemoveExist(context, checkFunction);
    }

    private void checkRemoveExist(ExcelCheckContext<AppealRemoveDO> context, Consumer<AppealRemoveDO> checkFunction) {
        Map<Integer, AppealRemoveDO> dataMap = context.getDataMap();
        LinkedHashMap<Integer, String> errorMap = context.getErrorMap();

        Iterator<Map.Entry<Integer, AppealRemoveDO>> iterator = dataMap.entrySet().iterator();

        while (iterator.hasNext()) {
            Map.Entry<Integer, AppealRemoveDO> entry = iterator.next();
            Integer rowNO = entry.getKey();
            AppealRemoveDO removeDO = entry.getValue();
            try {
                // 检查重复数据
                boolean exists = appealRemoveDomainService.existsDuplicate(
                        removeDO.getPeriod(),
                        removeDO.getTicketId(),
                        removeDO.getKpiName()
                );
                if (exists) {
                    errorMap.put(rowNO, String.format(
                            "在此绩效月下的指标数据剔除记录已存在：绩效月=%s, 单号=%s, 指标=%s",
                            removeDO.getPeriod(),
                            removeDO.getTicketId(),
                            removeDO.getKpiName()));
                    iterator.remove();
                    continue;
                }
                checkFunction.accept(removeDO);
            } catch (Exception e) {
                errorMap.put(rowNO, e.getMessage());
                iterator.remove();
            }
        }
    }

    private EasyExcelBatchImportListener<AppealRemoveDO, AppealRemoveImportVO> getImportListener(Consumer<ExcelCheckContext<AppealRemoveDO>> checkFunction) {
        Consumer<List<AppealRemoveDO>> saveFunction = appealRemoveDomainService::batchSave;
        Function<LinkedHashMap<Integer, AppealRemoveImportVO>, LinkedHashMap<Integer, AppealRemoveDO>> convertFunction =
            voMap -> {
                LinkedHashMap<Integer, AppealRemoveDO> result = new LinkedHashMap<>();
                voMap.forEach((k, v) -> result.put(k, AppealRemoveConverter.INSTANCE.importVO2DO(v)));
                return result;
            };
        EasyExcelBatchImportListener<AppealRemoveDO, AppealRemoveImportVO> importHelper =
            new EasyExcelBatchImportListener<>(saveFunction, AppealRemoveImportVO.class, checkFunction, convertFunction);
        return importHelper;
    }
}

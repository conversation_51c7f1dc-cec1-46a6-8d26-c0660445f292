package com.jd.kf.oss.performance.app.task;


import com.jd.kf.oss.performance.app.service.BasicConfigSyncService;
import com.jd.kf.oss.performance.app.task.request.PeriodParamPair;
import com.jd.schedule.annotation.DongJob;
import com.jd.schedule.client.handler.AbstractJobHandler;
import com.jd.schedule.context.JobContext;
import com.jd.schedule.model.JobResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@DongJob(name = "绩效因子配置同步任务")
@Slf4j
public class FactorConfigSyncTask extends AbstractJobHandler {

    @Autowired
    private BasicConfigSyncService basicConfigSyncService;

    @Override
    public JobResult execute(JobContext jobContext) throws Exception {
        String jobParam = jobContext.getJobParam();
        log.info("绩效因子配置同步任务请求入参，jobParam:{}", jobParam);
        PeriodParamPair periodParamPair = PeriodParamPair.parseOrDefaultPrevious(jobParam);
        basicConfigSyncService.factorConfigSyncTask(
                periodParamPair.getSourcePeriod(),
                periodParamPair.getTargetPeriod());
        return JobResult.SUCCESS;
    }
}

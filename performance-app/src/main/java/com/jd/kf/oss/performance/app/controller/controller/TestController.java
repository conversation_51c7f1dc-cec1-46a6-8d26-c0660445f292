package com.jd.kf.oss.performance.app.controller.controller;

//import com.alibaba.druid.support.json.JSONUtils;

import com.jd.kf.oss.performance.app.controller.dto.vo.ApiResult;
import com.jd.kf.oss.performance.app.controller.service.FileHelperService;
import com.jd.kf.oss.performance.app.service.UserInfoPeriodSyncService;
import com.jd.kf.oss.performance.infra.config.DynamicConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;


/**
 * Description: Web Demo
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/test")
public class TestController {
    @Autowired
    private UserInfoPeriodSyncService userInfoPeriodSyncService;


    @Autowired
    private FileHelperService fileHelperService;


    @Autowired
    private DynamicConfig dynamicConfig;


    /**
     * 测试接口，返回问候语
     */
    @GetMapping("/hello")
    public String hello() {
        return "hello world";
    }

    /**
     * 本地上传oss获取url
     */
    @PostMapping("/import/excel")
    public ApiResult<String> importExcelAndGetOssUrl(@RequestParam("file") MultipartFile excelFile){
        String fileOssUrl = fileHelperService.uploadFileAndGetNeverExpireUrl(excelFile);
        return ApiResult.success(fileOssUrl);
    }

    @GetMapping("/ducc")
    public String getDuccConfig() {
        return dynamicConfig.getDuccTest();
    }

}

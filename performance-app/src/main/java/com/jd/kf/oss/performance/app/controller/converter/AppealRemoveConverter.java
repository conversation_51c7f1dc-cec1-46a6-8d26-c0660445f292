package com.jd.kf.oss.performance.app.controller.converter;

import com.jd.kf.oss.performance.app.controller.dto.request.appeal.PageAppealRemoveRequest;
import com.jd.kf.oss.performance.app.controller.dto.vo.AppealRemoveVO;
import com.jd.kf.oss.performance.app.controller.dto.vo.appeal.AppealRemoveExportVO;
import com.jd.kf.oss.performance.app.controller.dto.vo.appeal.AppealRemoveImportVO;
import com.jd.kf.oss.performance.domain.config.domain.appeal.AppealRemoveDO;
import com.jd.kf.oss.performance.utils.CommonPage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 无效数据剔除对象转换器
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Mapper
public interface AppealRemoveConverter {
    
    AppealRemoveConverter INSTANCE = Mappers.getMapper(AppealRemoveConverter.class);

    /**
     * 分页请求参数转DO
     */
    AppealRemoveDO pageRequest2DO(PageAppealRemoveRequest request);

    /**
     * DO转VO
     */
    AppealRemoveVO do2VO(AppealRemoveDO appealRemoveDO);

    /**
     * 分页DO转分页VO
     */
    CommonPage<AppealRemoveVO> pageDO2PageVO(CommonPage<AppealRemoveDO> commonPage);

    /**
     * DO转导出VO
     */
    AppealRemoveExportVO do2ExportVO(AppealRemoveDO appealRemoveDO);

    /**
     * DO列表转导出VO列表
     */
    List<AppealRemoveExportVO> doList2ExportVOList(List<AppealRemoveDO> appealRemoveDOList);

    /**
     * 导入VO转DO
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "creator", source = "editor")
    @Mapping(target = "editor", source = "editor")
    @Mapping(target = "created", ignore = true)
    @Mapping(target = "modified", ignore = true)
    AppealRemoveDO importVO2DO(AppealRemoveImportVO importVO);

    /**
     * 导入VO列表转DO列表
     */
    List<AppealRemoveDO> importVOList2DOList(List<AppealRemoveImportVO> importVOList);
}

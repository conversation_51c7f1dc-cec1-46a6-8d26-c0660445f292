package com.jd.kf.oss.performance.app.task.request;

import com.jd.kf.oss.performance.utils.DateUtils;
import com.jd.security.auth.utils.codec.GsonUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class PeriodParamPair {

    private String sourcePeriod;

    private String TargetPeriod;


    public static PeriodParamPair parse(String periodPairParam) {
        if(StringUtils.isBlank(periodPairParam)){
            return null;
        }
        return GsonUtil.fromJson(periodPairParam, PeriodParamPair.class);
    }

    /**
     * 解析传入的时间段参数字符串，若解析失败则返回默认的上一个时间段
     * @param periodPairParam 待解析的时间段参数字符串
     * @return 解析成功返回PeriodParamPair对象，否则返回默认的绩效月时间对
     */
    public static PeriodParamPair parseOrDefaultPrevious(String periodPairParam) {
      PeriodParamPair periodParamPair = parse(periodPairParam);
      if(periodParamPair != null){
          return periodParamPair;
      }
      return defaultPreviousPeriod();
    }


    /**
     * 获取默认的上一个绩效月与当前绩效月的参数对
     * @return 包含上一个绩月和当前绩效月的PeriodParamPair对象
     */
    public static PeriodParamPair defaultPreviousPeriod(){
        String targetPeriod= DateUtils.getCurrentPerformancePeriod();
        String sourcePeriod=DateUtils.getPreviousPerformancePeriod(targetPeriod);
        PeriodParamPair periodParamPair = new PeriodParamPair();
        periodParamPair.setSourcePeriod(sourcePeriod);
        periodParamPair.setTargetPeriod(targetPeriod);
        return periodParamPair;
    }

}

package com.jd.kf.oss.performance.app.controller.service;

import com.alibaba.excel.EasyExcel;
import com.jd.dal.common.utils.StringUtils;
import com.jd.kf.oss.performance.app.controller.converter.EvaluateConverter;
import com.jd.kf.oss.performance.app.controller.dto.request.evaluate.ExportEvaluateRequest;
import com.jd.kf.oss.performance.app.controller.dto.vo.ApiResult;
import com.jd.kf.oss.performance.app.controller.dto.vo.evaluate.EvaluateExportVO;
import com.jd.kf.oss.performance.app.controller.dto.vo.evaluate.EvaluateImportVO;
import com.jd.kf.oss.performance.domain.config.domain.evaluate.EvaluateDO;
import com.jd.kf.oss.performance.domain.config.domain.evaluate.EvaluateDomainService;
import com.jd.kf.oss.performance.domain.config.aggregate.common.ExcelCheckContext;
import com.jd.kf.oss.performance.app.controller.utils.EasyExcelBatchImportListener;
import com.jd.kf.oss.performance.domain.config.domain.user.UserDO;
import com.jd.kf.oss.performance.domain.config.domain.user.UserDomainService;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * 上级评价Service
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@Service
public class EvaluateService {

    @Autowired
    private EvaluateDomainService evaluateDomainService;

    @Autowired
    private FileHelperService fileHelperService;

    @Autowired
    private UserDomainService userDomainService;

    /**
     * 导出上级评价数据
     * @param request 导出请求参数
     * @param httpServletRequest HTTP请求
     * @return OSS文件URL
     */
    public String exportEvaluate(ExportEvaluateRequest request, HttpServletRequest httpServletRequest) {
        // 查询数据
        List<EvaluateDO> evaluateList = evaluateDomainService.queryEvaluateList(
               request.getPeriod(), request.getMangerErp());
        // 校验导出数量限制
        fileHelperService.checkExportAmountLimit(evaluateList);
        // 转换为导出VO
        List<EvaluateExportVO> exportVOList = EvaluateConverter.INSTANCE.doList2ExportVOList(evaluateList);
        // 上传OSS并返回URL
        String fileName = "主管评价导出-" + request.getPeriod();
        return fileHelperService.uploadToOSS(httpServletRequest, fileName, EvaluateExportVO.class, exportVOList);
    }

    /**
     * 导入上级评价数据
     * @param url Excel文件url
     * @param httpServletRequest HTTP请求
     * @return 导入结果
     */
    public ApiResult<String> importEvaluate(String url, HttpServletRequest httpServletRequest) throws IOException {
        File excelFile = fileHelperService.downloadFile(url);
        fileHelperService.checkExcelFileRowCount(excelFile);
        Consumer<ExcelCheckContext<EvaluateDO>> checkFunction = this::checkImportData;
        EasyExcelBatchImportListener<EvaluateDO, EvaluateImportVO> importHelper = getImportListener(checkFunction);
        EasyExcel.read(excelFile, importHelper).sheet().doRead();
        if (com.jd.dal.common.utils.CollectionUtils.isEmpty(importHelper.getErrorMessages())) {
            return ApiResult.success();
        }
        String failedUrl = fileHelperService.uploadToOSS(httpServletRequest, "上级评价导入失败条目", importHelper.buildErrorSheet());
        return ApiResult.error(failedUrl);
    }

    @NotNull
    private EasyExcelBatchImportListener<EvaluateDO, EvaluateImportVO> getImportListener(Consumer<ExcelCheckContext<EvaluateDO>> checkFunction) {
        Consumer<List<EvaluateDO>> saveFunction = evaluateDomainService::batchSaveEvaluate;
        Function<LinkedHashMap<Integer, EvaluateImportVO>, LinkedHashMap<Integer, EvaluateDO>> convertFunction =
            voMap -> {
                LinkedHashMap<Integer, EvaluateDO> result = new LinkedHashMap<>();
                voMap.forEach((k, v) -> {
                    result.put(k, EvaluateConverter.INSTANCE.importVO2DO(v));
                });
                return result;
            };
        EasyExcelBatchImportListener<EvaluateDO, EvaluateImportVO> importHelper =
            new EasyExcelBatchImportListener<>(saveFunction, EvaluateImportVO.class, checkFunction, convertFunction);
        return importHelper;
    }

    private void checkImportData(ExcelCheckContext<EvaluateDO> context) {
        Consumer<EvaluateDO> checkFunction = EvaluateDO::validateForSave;
        checkEvaluateExist(context, checkFunction);
    }

    private void checkEvaluateExist(ExcelCheckContext<EvaluateDO> context, Consumer<EvaluateDO> checkFunction) {
        Map<Integer, EvaluateDO> dataMap = context.getDataMap();
        LinkedHashMap<Integer, String> errorMap = context.getErrorMap();

        // 按period分组收集erp列表
        Map<String, List<String>> periodToErpsMap = new HashMap<>();
        dataMap.forEach((k, v) -> {
            if (StringUtils.isNotBlank(v.getPeriod()) && StringUtils.isNotBlank(v.getErp())) {
                periodToErpsMap.computeIfAbsent(v.getPeriod(), period -> new ArrayList<>()).add(v.getErp());
            }
        });

        // 构建ERP+period组合键到UserDO的映射
        Map<String, UserDO> erpPeriodToUserMap = new HashMap<>();
        for (Map.Entry<String, List<String>> entry : periodToErpsMap.entrySet()) {
            String period = entry.getKey();
            List<String> erps = entry.getValue();

            List<UserDO> users = userDomainService.getUsersByErp(erps, period);
            for (UserDO user : users) {
                String key = user.getErp() + "_" + user.getPeriod();
                erpPeriodToUserMap.put(key, user);
            }
        }

        Iterator<Map.Entry<Integer, EvaluateDO>> iterator = dataMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Integer, EvaluateDO> entry = iterator.next();
            Integer rowNO = entry.getKey();
            EvaluateDO modifyDO = entry.getValue();
            try {
                // 构建ERP+period组合键进行查找
                String key = modifyDO.getErp() + "_" + modifyDO.getPeriod();
                if (!erpPeriodToUserMap.containsKey(key)) {
                    errorMap.put(rowNO, "该客服在指定绩效月不存在");
                    iterator.remove();
                    continue;
                }
                checkFunction.accept(modifyDO);
            } catch (IllegalArgumentException e) {
                errorMap.put(rowNO, e.getMessage());
                iterator.remove();
            }
        }
    }
}

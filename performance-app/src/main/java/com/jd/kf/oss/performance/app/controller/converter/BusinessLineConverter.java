package com.jd.kf.oss.performance.app.controller.converter;

import com.jd.kf.oss.performance.app.controller.dto.request.businessLine.BusinessLinePageRequest;
import com.jd.kf.oss.performance.app.controller.dto.request.businessLine.CreateBusinessLineRequest;
import com.jd.kf.oss.performance.app.controller.dto.request.businessLine.UpdateBusinessLineRequest;
import com.jd.kf.oss.performance.app.controller.dto.vo.businessLine.BusinessLinePageVO;
import com.jd.kf.oss.performance.domain.config.aggregate.configuration.composite.BusinessLineComposite;
import com.jd.kf.oss.performance.domain.config.domain.businessLine.BusinessLineDO;
import com.jd.kf.oss.performance.utils.CommonPage;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface BusinessLineConverter {
    BusinessLineConverter INSTANCE = Mappers.getMapper(BusinessLineConverter.class);


    BusinessLineDO request2DO(UpdateBusinessLineRequest request);

    BusinessLineDO request2DO(BusinessLinePageRequest request);

    BusinessLineDO request2DO(CreateBusinessLineRequest request);

    CommonPage<BusinessLinePageVO> pageDO2PageVO(CommonPage<BusinessLineDO> commonPage);

    // 新增：Composite转VO
    default BusinessLinePageVO composite2VO(BusinessLineComposite composite) {
        if (composite == null || composite.getBusinessLineDO() == null) {
            return null;
        }
        BusinessLinePageVO vo = new BusinessLinePageVO();
        BusinessLineDO doObj = composite.getBusinessLineDO();
        vo.setBusinessLineId(doObj.getBusinessLineId());
        vo.setName(doObj.getName());
        vo.setEditor(doObj.getEditor());
        vo.setModified(doObj.getModified());
        vo.setDescription(doObj.getDescription());
        vo.setPlanCode(composite.getPlanCode());
        vo.setPlanName(composite.getPlanName());
        vo.setChannel(doObj.getChannel());
        return vo;
    }
    default java.util.List<BusinessLinePageVO> compositeList2VOList(java.util.List<BusinessLineComposite> compositeList) {
        if (compositeList == null) return java.util.Collections.emptyList();
        java.util.List<BusinessLinePageVO> voList = new java.util.ArrayList<>();
        for (BusinessLineComposite composite : compositeList) {
            BusinessLinePageVO vo = composite2VO(composite);
            if (vo != null) voList.add(vo);
        }
        return voList;
    }
    // 新增：分页Composite转分页VO
    default CommonPage<BusinessLinePageVO> pageComposite2PageVO(CommonPage<BusinessLineComposite> page) {
        java.util.List<BusinessLinePageVO> voList = compositeList2VOList(page.getData());
        return CommonPage.getCommonPage(page.getPage(), page.getSize(), page.getTotal(), voList);
    }

    // 新增：DO分页转Composite分页
    default CommonPage<BusinessLineComposite> pageDO2CompositePage(CommonPage<BusinessLineDO> page) {
        if (page == null || page.getData() == null) {
            return CommonPage.emptyPage(0L, 0L);
        }
        java.util.List<BusinessLineComposite> compositeList = new java.util.ArrayList<>();
        for (BusinessLineDO businessLineDO : page.getData()) {
            BusinessLineComposite composite = new BusinessLineComposite();
            composite.setBusinessLineDO(businessLineDO);
            compositeList.add(composite);
        }
        return CommonPage.getCommonPage(page.getPage(), page.getSize(), page.getTotal(), compositeList);
    }
}

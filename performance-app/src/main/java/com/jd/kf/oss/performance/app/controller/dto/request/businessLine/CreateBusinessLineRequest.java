package com.jd.kf.oss.performance.app.controller.dto.request.businessLine;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreateBusinessLineRequest {
    private static final long serialVersionUID = 1L;

    /**
     * 绩效组名
     */
    @ApiModelProperty("绩效组名称")
    @NotNull(message = "绩效组不能为空")
    private String name;

    /**
     * 绩效组描述
     */
    @ApiModelProperty("绩效组描述")
    private String description;

    /**
     * 渠道
     */
    @ApiModelProperty("渠道")
    private String channel;
}

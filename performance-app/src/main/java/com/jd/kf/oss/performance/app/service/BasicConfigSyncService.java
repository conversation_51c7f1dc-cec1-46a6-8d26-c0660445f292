package com.jd.kf.oss.performance.app.service;


import com.jd.kf.oss.performance.context.UserContextHolder;
import com.jd.kf.oss.performance.domain.config.domain.coefficient.CoefficientDO;
import com.jd.kf.oss.performance.domain.config.domain.coefficient.CoefficientDomainService;
import com.jd.kf.oss.performance.domain.config.domain.factor.FactorDO;
import com.jd.kf.oss.performance.domain.config.domain.factor.FactorDomainService;
import com.jd.kf.oss.performance.domain.config.domain.plan.PlanDO;
import com.jd.kf.oss.performance.domain.config.domain.plan.PlanDomainService;
import com.jd.kf.oss.performance.utils.CheckUtil;
import com.jd.kf.oss.performance.utils.DateUtils;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 同步绩效系数、因子、方案等基础配置
 */
@Service
@Slf4j
public class BasicConfigSyncService {

    private final static int BATCH_SIZE = 1000;

    @Autowired
    private CoefficientDomainService coefficientDomainService;

    @Autowired
    private FactorDomainService factorDomainService;


    @Autowired
    private PlanDomainService planDomainService;




    /**
     * 同步当前绩效月的绩效系数配置（从上一绩效月复制）
     */
    public void syncPreviousPeriodCoefficientConfig(){
        String targetPeriod= DateUtils.getCurrentPerformancePeriod();
        String sourcePeriod=DateUtils.getPreviousPerformancePeriod(targetPeriod);
        syncCoefficientConfig(sourcePeriod, targetPeriod);
    }


    /**
     * 执行绩效系数配置同步任务
     */
    public void coefficientConfigSyncTask(String sourcePeriod, String targetPeriod) {
        CallerInfo callerInfo = Profiler.registerInfo("performance-coefficientPeriodSyncTask");
        try {
            log.info("[绩效系数配置同步任务] 开始");
            syncCoefficientConfig(sourcePeriod,targetPeriod);
            log.info("[绩效系数配置同步任务] 完成");
        } catch (Exception e) {
            log.error("[绩效系数配置同步任务] 失败:{}", e.getMessage(),e);
            Profiler.functionError(callerInfo);
            throw e;
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }


    public void syncCoefficientConfig(String sourcePeriod,String targetPeriod) {
        UserContextHolder.initSuperTenantContext();
        checkPeriod(sourcePeriod,targetPeriod);
        int count= coefficientDomainService.countCoefficientDOByPeriod(targetPeriod);
        CheckUtil.isTrue(count==0, "目标绩效月"+targetPeriod+"已存在绩效系数配置，请勿重复同步");
        List<CoefficientDO> dosOfPreviousPeriod=coefficientDomainService.queryAllCoefficientDOByPeriod(sourcePeriod);
        batchCopyCoefficient(dosOfPreviousPeriod,targetPeriod);
    }


    /**
     * 批量复制绩效系数数据
     */
    public void batchCopyCoefficient(List<CoefficientDO> coefficientDOS,String targetPeriod){
        if(CollectionUtils.isEmpty(coefficientDOS)){
            return ;
        }
        // 修改创建时间，更新时间，操作人
        coefficientDomainService.modifyCoefficientDOMetaInfoForSync(coefficientDOS,targetPeriod);
        // 批量插入修改后的绩效系数
        coefficientDomainService.batchSaveCoefficientDOWithLimit(coefficientDOS,BATCH_SIZE);
    }


    /**
     * 同步当前绩效月的绩效系数配置（从上一绩效月复制）
     */
    public void syncPreviousPeriodFactorConfig(){
        String targetPeriod= DateUtils.getCurrentPerformancePeriod();
        String sourcePeriod=DateUtils.getPreviousPerformancePeriod(targetPeriod);
        syncFactorConfig(sourcePeriod, targetPeriod);
    }



    /**
     * 执行绩效因子配置同步任务
     */
    public void factorConfigSyncTask(String sourcePeriod, String targetPeriod) {
        CallerInfo callerInfo = Profiler.registerInfo("performance-factorPeriodSyncTask");
        try {
            log.info("[绩效因子配置同步任务] 开始");
            syncFactorConfig(sourcePeriod,targetPeriod);
            log.info("[绩效因子配置同步任务] 完成");
        } catch (Exception e) {
            log.error("[绩效因子配置同步任务] 失败:{}", e.getMessage(),e);
            Profiler.functionError(callerInfo);
            throw e;
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }


    public void syncFactorConfig(String sourcePeriod,String targetPeriod) {
        UserContextHolder.initSuperTenantContext();
        checkPeriod(sourcePeriod,targetPeriod);
        int count= factorDomainService.countAllFactorDOByPeriod(targetPeriod);
        CheckUtil.isTrue(count==0, "目标绩效月"+targetPeriod+"已存在绩效因子配置，请勿重复同步");
        List<FactorDO> dosOfPreviousPeriod=factorDomainService.queryAllFactorDOByPeriod(sourcePeriod);
        batchCopyFactor(dosOfPreviousPeriod,targetPeriod);
    }



    /**
     * 批量复制绩效因子数据并保存
     */
    public void batchCopyFactor(List<FactorDO> factorDOS,String targetPeriod){
        if(CollectionUtils.isEmpty(factorDOS)){
            return ;
        }
        // 修改创建时间，更新时间，操作人
        factorDomainService.modifyFactorDOMetaInfoForSync(factorDOS,targetPeriod);
        // 批量插入修改后的绩效因子
        factorDomainService.batchSaveFactorDOWithLimit(factorDOS,BATCH_SIZE);
    }

    /**
     * 同步当前绩效月的绩效系数配置（从上一绩效月复制）
     */
    public void syncPreviousPeriodPlanConfig(){
        String targetPeriod= DateUtils.getCurrentPerformancePeriod();
        String sourcePeriod=DateUtils.getPreviousPerformancePeriod(targetPeriod);
        syncPlanConfig(sourcePeriod, targetPeriod);
    }


    /**
     * 执行绩效方案配置同步任务
     */
    public void planConfigSyncTask(String sourcePeriod, String targetPeriod) {
        CallerInfo callerInfo = Profiler.registerInfo("performance-planPeriodSyncTask");
        try {
            log.info("[绩效方案配置同步任务] 开始");
            syncPlanConfig(sourcePeriod,targetPeriod);
            log.info("[绩效方案配置同步任务] 完成");
        } catch (Exception e) {
            log.error("[绩效方案配置同步任务] 失败:{}", e.getMessage(),e);
            Profiler.functionError(callerInfo);
            throw e;
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }


    public void syncPlanConfig(String sourcePeriod,String targetPeriod) {
        UserContextHolder.initSuperTenantContext();
        checkPeriod(sourcePeriod,targetPeriod);
        int count= planDomainService.countPlanDOByPeriod(targetPeriod);
        CheckUtil.isTrue(count==0, "目标绩效月"+targetPeriod+"已存在绩效方案配置，请勿重复同步");
        List<PlanDO> dosOfPreviousPeriod=planDomainService.queryAllPlanDOByPeriod(sourcePeriod);
        batchCopyPlan(dosOfPreviousPeriod,targetPeriod);
    }

    /**
     * 批量复制绩效方案
     */
    public void batchCopyPlan(List<PlanDO> planDOS,String targetPeriod){
        if(CollectionUtils.isEmpty(planDOS)){
            return ;
        }
        // 修改创建时间，更新时间，操作人
        planDomainService.modifyPlanDOMetaInfoForSync(planDOS,targetPeriod);
        // 批量插入修改后的绩效方案
        planDomainService.batchSavePlanDOWithLimit(planDOS,BATCH_SIZE);
    }


    private void checkPeriod(String sourcePeriod, String targetPeriod) {
        CheckUtil.notBlank(sourcePeriod, "源绩效月不能为空");
        CheckUtil.notBlank(targetPeriod, "目标绩效月不能为空");
        CheckUtil.isFalse(sourcePeriod.equals(targetPeriod), "源绩效月和目标绩效月不能相同");
    }

}

package com.jd.kf.oss.performance.app.controller.converter;

import com.jd.kf.oss.performance.app.controller.dto.request.appeal.PageAppealModifyRequest;
import com.jd.kf.oss.performance.app.controller.dto.vo.AppealModifyVO;
import com.jd.kf.oss.performance.app.controller.dto.vo.appeal.AppealModifyExportVO;
import com.jd.kf.oss.performance.app.controller.dto.vo.appeal.AppealModifyImportVO;
import com.jd.kf.oss.performance.domain.config.domain.appeal.AppealModifyDO;
import com.jd.kf.oss.performance.utils.CommonPage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 归属数据修改对象转换器
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Mapper
public interface AppealModifyConverter {
    
    AppealModifyConverter INSTANCE = Mappers.getMapper(AppealModifyConverter.class);

    /**
     * 分页请求参数转DO
     */
    AppealModifyDO pageRequest2DO(PageAppealModifyRequest request);



    /**
     * DO转VO
     */
    AppealModifyVO do2VO(AppealModifyDO appealModifyDO);

    /**
     * 分页DO转分页VO
     */
    CommonPage<AppealModifyVO> pageDO2PageVO(CommonPage<AppealModifyDO> commonPage);

    /**
     * DO转导出VO
     */
    AppealModifyExportVO do2ExportVO(AppealModifyDO appealModifyDO);

    /**
     * DO列表转导出VO列表
     */
    List<AppealModifyExportVO> doList2ExportVOList(List<AppealModifyDO> appealModifyDOList);

    /**
     * 导入VO转DO
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "indexName", ignore = true)
    @Mapping(target = "ticketId", source = "billId")
    @Mapping(target = "skillId", source = "skillId")
    @Mapping(target = "creator", source = "editor")
    @Mapping(target = "editor", source = "editor")
    @Mapping(target = "created", ignore = true)
    @Mapping(target = "modified", ignore = true)
    AppealModifyDO importVO2DO(AppealModifyImportVO importVO);

    /**
     * 导入VO列表转DO列表
     */
    List<AppealModifyDO> importVOList2DOList(List<AppealModifyImportVO> importVOList);
}

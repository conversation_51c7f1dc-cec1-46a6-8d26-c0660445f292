package com.jd.kf.oss.performance.app.controller.controller;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jd.dal.common.utils.CollectionUtils;
import com.jd.dal.common.utils.StringUtils;
import com.jd.kf.oss.performance.app.controller.converter.FactorConverter;
import com.jd.kf.oss.performance.app.controller.dto.request.factor.FactorDeleteRequest;
import com.jd.kf.oss.performance.app.controller.dto.request.factor.FactorRequest;
import com.jd.kf.oss.performance.app.controller.dto.request.factor.FactorSaveRequest;
import com.jd.kf.oss.performance.app.controller.dto.request.factor.FactorTestCalcRequest;
import com.jd.kf.oss.performance.app.controller.dto.vo.ApiResult;
import com.jd.kf.oss.performance.app.controller.dto.vo.factor.FactorVO;
import com.jd.kf.oss.performance.app.controller.dto.vo.factor.PerformanceFactorResultDetailVO;
import com.jd.kf.oss.performance.app.controller.dto.vo.factor.PerformanceResultVO;
import com.jd.kf.oss.performance.context.UserContextHolder;
import com.jd.kf.oss.performance.domain.config.aggregate.configuration.PlanAggregateService;
import com.jd.kf.oss.performance.domain.config.domain.factor.FactorDO;
import com.jd.kf.oss.performance.domain.config.domain.factor.FactorDomainService;
import com.jd.kf.oss.performance.domain.runtime.aggregate.PerformanceTaskAggregateService;
import com.jd.kf.oss.performance.domain.runtime.domain.TaskElementTypeEnum;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceresult.PerformanceFactorResultDO;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceresult.PerformanceProcessResult;
import com.jd.kf.oss.performance.domain.runtime.domain.performanceuser.PerformanceUserDO;
import com.jd.kf.oss.performance.utils.CommonPage;
import com.jd.kf.oss.performance.utils.DateUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Api(tags = "绩效因子管理")
@RestController
@RequestMapping("/performance/factor")
@Slf4j
public class FactorController {

    @Resource
    private FactorDomainService factorDomainService;

    @Resource
    private PlanAggregateService planAggregateService;

    @Resource
    private PerformanceTaskAggregateService performanceTaskAggregateService;

    /**
     * 查询绩效因子列表
     * @param request 查询请求参数
     * @return 绩效因子查询结果
     */
    @ApiOperation("查询绩效因子")
    @PostMapping("/list")
    public ApiResult<CommonPage<FactorVO>> list(@RequestBody @Validated FactorRequest request) {
        CommonPage<FactorDO> factorDOCommonPage = factorDomainService.queryFactors(
                UserContextHolder.getTenantCode(),
                DateUtils.getCurrentPerformancePeriod(),
                request.getName(),
                request.getType(),
                request.getPageNum(),
                request.getPageSize()
        );
        CommonPage<FactorVO> result = new CommonPage<>();
        result.setSize(factorDOCommonPage.getSize());
        result.setPage(factorDOCommonPage.getPage());
        result.setTotal(factorDOCommonPage.getTotal());
        if (CollectionUtils.isNotEmpty(factorDOCommonPage.getData())) {
            result.setData(
                    factorDOCommonPage.getData().stream().map(FactorConverter.INSTANCE::do2VO)
                            .collect(Collectors.toList())
            );
        }
        return ApiResult.success(result);
    }

    /**
     * 查询绩效因子
     * @param request 创建绩效组请求对象，包含绩效组相关信息
     * @return 操作结果，成功返回成功响应，失败返回错误响应
     */
    @ApiOperation("保存绩效因子")
    @PostMapping("/save")
    public ApiResult<Long> save(@RequestBody @Validated FactorSaveRequest request) {
        request.setPeriod(DateUtils.getCurrentPerformancePeriod());
        FactorDO factorDO = FactorConverter.INSTANCE.do2DO(request);
        factorDO.setTenantCode(UserContextHolder.getTenantCode());
        if (Objects.isNull(factorDO.getId()) && Objects.isNull(factorDO.getCode())) {
            factorDO.setCode(factorDO.buildCode());
        }
        boolean checkResult = false;
        try {
            checkResult = factorDomainService.checkCircle(factorDO);
        } catch (Exception e) {
            return ApiResult.error("因子公式解析报错");
        }
        if (!checkResult) {
            Long result = factorDO.save();
            return ApiResult.success(result);
        } else {
            return ApiResult.error("因子公式解析有环出现");
        }
    }

    /**
     * 查询绩效因子
     * @param request 创建绩效组请求对象，包含绩效组相关信息
     * @return 操作结果，成功返回成功响应，失败返回错误响应
     */
    @ApiOperation("校验是否成环")
    @PostMapping("/checkCircle")
    public ApiResult<Boolean> checkCircle(@RequestBody @Validated FactorSaveRequest request) {
        request.setPeriod(DateUtils.getCurrentPerformancePeriod());
        request.setTenantCode(UserContextHolder.getTenantCode());
        FactorDO factorDO = FactorConverter.INSTANCE.do2DO(request);
        Boolean result = false;
        try {
            result = factorDomainService.checkCircle(factorDO);
        } catch (Exception e) {
            return ApiResult.error("公式解析失败");
        }
        return ApiResult.success(result);
    }

    /**
     * 删除绩效因子
     * @param request 删除绩效因子请求对象
     * @return 操作结果，成功返回成功响应，失败返回错误响应
     */
    @ApiOperation("删除绩效因子")
    @PostMapping("/delete")
    public ApiResult<Boolean> delete(@RequestBody @Validated FactorDeleteRequest request) {
        request.setPeriod(DateUtils.getCurrentPerformancePeriod());
        FactorDO factorDO = new FactorDO(UserContextHolder.getTenantCode(), request.getCode(), request.getPeriod());
        factorDO = factorDO.loadByCode();
        boolean flag = planAggregateService.weatherFactorUsed(request.getCode(), request.getPeriod());
        if (flag) {
            return ApiResult.error("绩效因子被引用，无法被删除");
        }
        Boolean result = factorDO.delete();
        return ApiResult.success(result);
    }

    @ApiOperation("计算因子结果")
    @PostMapping("/testCalc")
    public ApiResult<PerformanceResultVO> testCalc(@RequestBody @Validated FactorTestCalcRequest request) {
        PerformanceUserDO userDO = performanceTaskAggregateService.calcTest(UserContextHolder.getTenantCode(), request.getPeriod(),request.getFormula(), request.getErp());

        PerformanceResultVO vo = new PerformanceResultVO();
        vo.setErp(request.getErp());
        vo.setResult(userDO.getPerformanceProcessResult().getResult());
        vo.setDetail(userDO.getPerformanceProcessResult().getDetail());

        List<PerformanceProcessResult> processResultList = Lists.newArrayList(userDO.getFactorResultMap().values());

        List<PerformanceFactorResultDO> resultDOS = processResultList.stream().filter(a -> org.apache.commons.lang3.StringUtils.equalsAnyIgnoreCase("已完成", a.getStatus())).map(PerformanceTaskAggregateService::convertToFactorResultDO).collect(Collectors.toList());

        List<PerformanceFactorResultDetailVO> detailVOList = Lists.newArrayList();
        for (PerformanceFactorResultDO result : resultDOS) {
            PerformanceFactorResultDetailVO detailVO = new PerformanceFactorResultDetailVO();
            detailVO.setCode(result.getFactorId());
            detailVO.setErp(result.getErp());
            detailVO.setBusinessLineId(result.getBusinessLineId());
            detailVO.setBusinessLineName(result.getBusinessLineName());
            detailVO.setDetail(result.getDetail());
            detailVO.setResult(result.getResult());
            detailVOList.add(detailVO);
        }
        vo.setFactorResults(detailVOList);
        return ApiResult.success(vo);
    }
}

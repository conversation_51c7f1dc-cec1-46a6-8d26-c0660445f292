package com.jd.kf.oss.performance.app.task.runtime;

import com.jd.kf.oss.performance.context.UserContextHolder;
import com.jd.kf.oss.performance.domain.config.domain.businessLine.BusinessLineDO;
import com.jd.kf.oss.performance.domain.config.domain.businessLine.BusinessLineDomainService;
import com.jd.kf.oss.performance.domain.config.domain.performancetarget.PerformanceTargetDO;
import com.jd.kf.oss.performance.domain.config.domain.performancetarget.PerformanceTargetDomainService;
import com.jd.kf.oss.performance.domain.runtime.domain.performancetask.PerformanceTaskDO;
import com.jd.schedule.annotation.DongJob;
import com.jd.schedule.client.handler.AbstractJobHandler;
import com.jd.schedule.context.JobContext;
import com.jd.schedule.model.JobResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/06/30
 * 绩效组
 */
@Component
@DongJob(name = "绩效组绩效月创建计算任务")
@Slf4j
public class PerformanceTask extends AbstractJobHandler {
    @Autowired
    private BusinessLineDomainService businessLineDomainService;

    @Resource
    private PerformanceTargetDomainService performanceTargetDomainService;

    /**
     * @param jobContext
     * @return {@link JobResult }
     */
    @Override
    public JobResult execute(JobContext jobContext) {
        String tenantCode = "kf_perform_retail";
        String period = "2025-07";
        UserContextHolder.initAndSetUserContext(tenantCode, "liushangpeng1");

        List<PerformanceTargetDO> performanceTargetDOS = performanceTargetDomainService.queryAllTargetByPeriod(period);
        List<String> businessLineIds = performanceTargetDOS.stream().filter(a -> StringUtils.isNotBlank(a.getEvaluationPlanCode())).map(PerformanceTargetDO::getBusinessLineId).collect(Collectors.toList());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(businessLineIds)) {
            return JobResult.SUCCESS;
        }
        List<BusinessLineDO> businessLines = businessLineDomainService.getBusinessLineByLineIds(businessLineIds);
        PerformanceTaskDO performanceTaskDO = new PerformanceTaskDO();
        performanceTaskDO.create(UserContextHolder.getTenantCode(), period, businessLines);

        return JobResult.SUCCESS;
    }
}

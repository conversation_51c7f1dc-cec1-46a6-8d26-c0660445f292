spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
# writeDb config
spring.datasource.druid.write.url=*********************************************************************************************************************************
spring.datasource.druid.write.username=imtest_rw
spring.datasource.druid.write.password=5iht9BBHknVCMLQw
spring.datasource.druid.write.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.druid.write.initial-size=5
spring.datasource.druid.write.max-active=5
spring.datasource.druid.write.min-idle=1
spring.datasource.druid.write.max-wait=60000
spring.datasource.druid.write.pool-prepared-statements=false
spring.datasource.druid.write.validation-query=select 1
spring.datasource.druid.write.validation-query-timeout=1
spring.datasource.druid.write.test-on-borrow=false
spring.datasource.druid.write.test-on-return=false
spring.datasource.druid.write.test-while-idle=true
spring.datasource.druid.write.time-between-eviction-runs-millis=10000
spring.datasource.druid.write.filters=log4j2,wall
# readDb config
spring.datasource.druid.read.url=*********************************************************************************************************************************
#spring.datasource.druid.read.username=root
#spring.datasource.druid.read.password=root
spring.datasource.druid.read.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.druid.read.initial-size=5
spring.datasource.druid.read.max-active=5
spring.datasource.druid.read.min-idle=1
spring.datasource.druid.read.max-wait=60000
spring.datasource.druid.read.pool-prepared-statements=false
spring.datasource.druid.read.validation-query=select 1
spring.datasource.druid.read.validation-query-timeout=1
spring.datasource.druid.read.test-on-borrow=false
spring.datasource.druid.read.test-on-return=false
spring.datasource.druid.read.test-while-idle=true
spring.datasource.druid.read.time-between-eviction-runs-millis=10000
spring.datasource.druid.read.filters=log4j2,wall


# assignWriteDb config
spring.datasource.druid.assign.write.url=***************************************************************************************************
spring.datasource.druid.assign.write.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.druid.assign.write.initial-size=5
spring.datasource.druid.assign.write.max-active=5
spring.datasource.druid.assign.write.min-idle=1
spring.datasource.druid.assign.write.max-wait=60000
spring.datasource.druid.assign.write.pool-prepared-statements=false
spring.datasource.druid.assign.write.validation-query=select 1
spring.datasource.druid.assign.write.validation-query-timeout=1
spring.datasource.druid.assign.write.test-on-borrow=false
spring.datasource.druid.assign.write.test-on-return=false
spring.datasource.druid.assign.write.test-while-idle=true
spring.datasource.druid.assign.write.time-between-eviction-runs-millis=10000
spring.datasource.druid.assign.write.filters=log4j2,wall

# jmq
uadtask.topic=referee_to_zs_message_pre
jsl.jsf.alias=jdtest
crm.jsf.alias=crm_paas_prod_lf

jmq4.address=nameserver.jmq.jd.local:80
jmq4.username=wfmschedule
jmq4.password=6b4d6da61225414c94205247ba5815ca
jmq4.app=wfmschedule

csim.jsf.alias=csim-dev
csim.jsf.token=10GAE51V9

jsf.alias.scheduleDevService = dd0

# jsf
#jsf.registry.index=i.jsf.jd.com
#jsl.jsf.alias=jsl-zyx
#jsf.provider.alias=jdtest

laf.config.manager.application=jdos_performance-center
laf.config.manager.resources[0].name=performance-center
laf.config.manager.resources[0].uri=ucc://jdos_performance-center:<EMAIL>/v1/namespace/jdos_performance-center/config/online/profiles/DUCC_DEFAULT_PROFILE?longPolling=60000&necessary=true
laf.config.manager.parameters[0].name=autoListener
laf.config.manager.parameters[0].value=true
laf.config.logger.enabled=true
laf.config.logger.type=log4j2
laf.config.logger.key=logger.level


package com.jd.kf.oss.performance.utils.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.jd.kf.oss.performance.exception.BizException;
import com.jd.kf.oss.performance.utils.AgentTypeEnum;
import lombok.Getter;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;

/**
 * @author:zmf
 * @date:2021/8/26
 */
public class EasyExcelUtils {

    private static final String agent = "User-Agent";

    public static <T> ExcelWriter buildExcel(Class<T> tClass, OutputStream outputStream) {
        return new ExcelWriterBuilder()
                .file(outputStream)
                .excelType(ExcelTypeEnum.XLSX)
                .head(tClass)
                .build();
    }

    public static <T> ExcelWriter buildExcel(String fileName, Class<T> tClass, HttpServletResponse response) {
        OutputStream outputStream = getOutputStream(fileName, response, ExcelTypeEnum.XLSX.getValue());
        return new ExcelWriterBuilder()
                .file(outputStream)
                .excelType(ExcelTypeEnum.XLSX)
                .head(tClass)
                .build();
    }

    public static <T> void writeExcelSheet(ExcelWriter excelWriter, WriteSheet writeSheet,
                                           EasyExcelExportSheetExtract<T> sheetExtract) {
        writeSheet.setSheetNo(sheetExtract.getSheetNo());
        writeSheet.setSheetName(sheetExtract.getSheetName());
        excelWriter.write(sheetExtract.getSheetExtractData(), writeSheet);
    }

    public static void finish(ExcelWriter excelWriter) {
        excelWriter.finish();
    }

    public static WriteSheet buildWriteSheet() {
        return new WriteSheet();
    }

    public static <T> ByteArrayOutputStream exportSingleSheet(String sheetName, Class<T> tClass, List<T> data,
                                             HttpServletRequest request) {
        EasyExcelExportSheetExtract<T> sheetExtract = buildSingleExportSheet(data,sheetName);
        ExcelTypeEnum excelType = getExcelTypeByAgentOrDefault(request);
        try{
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            exportByClassMapping( tClass ,sheetExtract,outputStream,excelType);
            return outputStream;
        }catch (Exception e){
            throw new BizException("导出excel表格失败!", e);
        }
    }

    private static <T> EasyExcelExportSheetExtract<T> buildSingleExportSheet(List<T> data,String filename) {
        EasyExcelExportSheetExtract<T> sheetExtract = new EasyExcelExportSheetExtract<>();
        sheetExtract.setSheetExtractData(data);
        sheetExtract.setSheetNo(1);
        sheetExtract.setSheetName(filename);
        return sheetExtract;
    }


    public static <T> void exportByClassMapping(Class<T>  tClass,
                                              EasyExcelExportSheetExtract<T> sheetExtract,
                                              OutputStream outputStream,
                                              ExcelTypeEnum excelType) {

        ExcelWriter excelWriter = new ExcelWriterBuilder().file(outputStream).excelType(excelType).head(tClass).build();
        try {
            WriteSheet writeSheet = buildWriteSheet();
            writeExcelSheet(excelWriter, writeSheet, sheetExtract);
        }catch (Exception e){
            throw new BizException("导出excel表格失败!", e);
        }finally {
            finish(excelWriter);
        }

    }

    /**
     * 通过客户端类型选择文件后缀
     *
     * @param httpServletRequest
     * @return
     */
    public static ExcelTypeEnum getExcelTypeByAgentOrDefault(HttpServletRequest httpServletRequest) {
        return AgentTypeEnum.valueOfAgents(httpServletRequest.getHeader(agent));
    }




    public static ByteArrayOutputStream generateExcelAndExport(List<EasyExcelExportStringSheetExtract> stringSheetExtracts,
                                                               ExcelTypeEnum excelType) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        generateExcelAndExport(stringSheetExtracts, excelType, outputStream);
        return outputStream;
    }

    public static void generateExcelAndExport(List<EasyExcelExportStringSheetExtract> stringSheetExtracts,
                                              ExcelTypeEnum excelType,OutputStream outputStream) {

        try(ExcelWriter excelWriter = new ExcelWriterBuilder().file(outputStream).excelType(excelType).build()){
            for (EasyExcelExportStringSheetExtract sheetExtract : stringSheetExtracts) {
                WriteSheet writeSheet = new WriteSheet();
                writeSheet.setHead(sheetExtract.getHeads());
                writeSheet.setSheetNo(sheetExtract.getSheetNo());
                writeSheet.setSheetName(sheetExtract.getSheetName());
                excelWriter.write(sheetExtract.getDataList(), writeSheet);
            }
        } catch (Exception e) {
            throw new BizException("导出excel表格失败!"+e.getMessage(), e);
        }
    }


    public static ByteArrayOutputStream generateExcelAndExport(EasyExcelExportStringSheetExtract stringSheetExtract,
                                              HttpServletRequest httpServletRequest) {
        List<EasyExcelExportStringSheetExtract> sheetExtracts = new ArrayList<>();
        sheetExtracts.add(stringSheetExtract);
        ExcelTypeEnum excelType = getExcelTypeByAgentOrDefault(httpServletRequest);
        return generateExcelAndExport(sheetExtracts, excelType);
    }

    private static OutputStream getOutputStream(String fileName, HttpServletResponse response, String fileFormat) {
        try {
            fileName = URLEncoder.encode(fileName+fileFormat, "UTF-8");
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf8");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
            response.setHeader("Pragma", "public");
            response.setHeader("Cache-Control", "no-store");
            response.addHeader("Cache-Control", "max-age=0");
            return response.getOutputStream();
        } catch (Exception e) {
            throw new BizException("导出excel表格失败!", e);
        }
    }

    public static Integer countFileRows(File file) throws IOException {
        CountRowsListener countRowsListener = new CountRowsListener();
        EasyExcel.read(file,countRowsListener).sheet().doRead();
        return countRowsListener.getTotalRowCount();
    }

    @Getter
    public static class CountRowsListener implements   ReadListener<Object> {
        private  Integer totalRowCount=0;
        @Override
        public void invoke(Object data, AnalysisContext context) {
            totalRowCount++;
        }
        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {

        }
    }

}

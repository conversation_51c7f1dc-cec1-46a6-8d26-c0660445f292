package com.jd.kf.oss.performance.utils;

import org.apache.commons.lang3.tuple.Pair;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Date;


/**
 * 时间计算工具类
 */
public class DateUtils {
    public static final String DATE_FMT_yyyy_MM = "yyyy-MM";

    public static final String TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    public static final String DATE_FMT_yyyy_MM_dd = "yyyy-MM-dd";

    private static final String zoneId = "Asia/Shanghai";
    /**
     * string 转 LocalDateTime
     *
     * @param dateStr 例："2017-08-11 01:00:00"
     * @param format  例："yyyy-MM-dd HH:mm:ss"
     */
    public static LocalDateTime stringToLocalDateTime(String dateStr, String format) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
            return LocalDateTime.parse(dateStr, formatter);
        } catch (DateTimeParseException e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 获取当前时间
     *
     * @return LocalDateTime
     */
    public static LocalDateTime getCurrentLocalDateTime() {
        return LocalDateTime.now(Clock.system(ZoneId.of(zoneId)));

    }

    public static LocalDateTime now() {
        return getCurrentLocalDateTime();
    }



    /**
     * 获取当前日期对应的绩效月份（以每月21日为分界点）
     * @return 返回格式为"yyyy-MM"的绩效月份字符串
     */
    public static String getCurrentPerformancePeriod() {
        LocalDate now = LocalDate.now();
        // 取本月21日
        LocalDate thisMonth21 = now.withDayOfMonth(21);
        String period;
        if (now.isBefore(thisMonth21)) {
            // 21号之前，属于上一个绩效月
            LocalDate lastMonth = now.minusMonths(1);
            period = lastMonth.plusMonths(1).format(DateTimeFormatter.ofPattern(DATE_FMT_yyyy_MM));
        } else {
            // 21号及以后，属于下一个绩效月
            period = now.plusMonths(1).format(DateTimeFormatter.ofPattern(DATE_FMT_yyyy_MM));
        }
        return period;
    }


    /**
     * 获取指定月份的上一个月份
     * @param period 指定的月份字符串，格式为"yyyy-MM"
     * @return 返回上一个月份的字符串，格式为"yyyy-MM"
     */
    public static String getPreviousPerformancePeriod(String period) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_FMT_yyyy_MM);
        YearMonth yearMonth = YearMonth.parse(period, formatter);
        YearMonth preMonth = yearMonth.minusMonths(1);
        return preMonth.format(DateTimeFormatter.ofPattern(DATE_FMT_yyyy_MM));
    }


    /**
     * 将字符串解析为LocalDate对象
     * @param date 符合ISO_LOCAL_DATE格式(yyyy-MM-dd)的日期字符串
     * @return 解析成功返回对应的LocalDate对象
     * @throws IllegalArgumentException 当日期格式不符合要求或日期无效时抛出
     */
    public static LocalDate parseStringToLocalDate(String date) {
        try {
//            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_FMT_yyyy_MM_dd);
            DateTimeFormatter formatter = DateTimeFormatter.ISO_LOCAL_DATE;
            return LocalDate.parse(date, formatter);
        }catch (DateTimeParseException e) {
            throw new IllegalArgumentException("请使用yyyy-MM-dd日期格式，并确保日期有效");
        }
    }

    public static boolean notAfter(String startDate, String endDate){
        LocalDate start = parseStringToLocalDate(startDate);
        LocalDate end = parseStringToLocalDate(endDate);
        return !start.isAfter(end);
    }



    /**
     * 获取当前考核周期的范围
     * @param now 当前日期
     * @return 返回一个Pair对象，左侧为开始日期，右侧为结束日期
     */
    public static Pair<LocalDate,LocalDate> getCheckUpRangeOfPeriod(LocalDate now) {
        LocalDate thisMonth21 = now.withDayOfMonth(21);
        LocalDate startDate;
        LocalDate endDate;

        if (now.isBefore(thisMonth21)) {
            // 21号之前，获取上一个月1号到这个月的月末
            startDate = now.minusMonths(1).withDayOfMonth(1);
            endDate = now.withDayOfMonth(now.lengthOfMonth());
        } else {
            // 21号及之后，获取本月1号到下个月月底
            startDate = now.withDayOfMonth(1);
            LocalDate nextMonth = now.plusMonths(1);
            endDate = nextMonth.withDayOfMonth(nextMonth.lengthOfMonth());
        }
        return Pair.of(startDate, endDate);
    }

    /**
     * 判断指定的日期区间是否被完全包含在当前绩效周期内
     * @param startDate 开始日期字符串
     * @param endDate 结束日期字符串
     * @return true表示被完全包含，false表示不被包含
     */
    public static boolean isContainedInCheckUpRange(String startDate, String endDate) {
        LocalDate start = parseStringToLocalDate(startDate);
        LocalDate end = parseStringToLocalDate(endDate);
        Pair<LocalDate, LocalDate> dateRangeOfCurrentPeriod = getCheckUpRangeOfPeriod(LocalDate.now());
        LocalDate periodStart = dateRangeOfCurrentPeriod.getLeft();
        LocalDate periodEnd = dateRangeOfCurrentPeriod.getRight();
        return !start.isBefore(periodStart) && !end.isAfter(periodEnd);
    }

    /**
     * 获取当前绩效月的校验限制周期范围
     * @param now 当前日期
     * @return 返回格式为"yyyy-MM-dd~yyyy-MM-dd"的字符串，表示当前绩效周期的开始和结束日期
     */
    public static String getStrCheckUpRangeOfPeriod(LocalDate now) {
        Pair<LocalDate, LocalDate> dateRangeOfPeriod = getCheckUpRangeOfPeriod(now);
        LocalDate start = dateRangeOfPeriod.getLeft();
        LocalDate end = dateRangeOfPeriod.getRight();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_FMT_yyyy_MM_dd);
        return start.format(formatter) + "~" + end.format(formatter);
    }

    public static String getCurrentCheckUpRange() {
        return getStrCheckUpRangeOfPeriod(LocalDate.now());
    }

    /**
     * 判断date1是否早于date2 (LocalDateTime版本), 两个时间相等返回true
     */
    public static boolean isEqualOrBefore(LocalDateTime date1, LocalDateTime date2) {
        if (date1 == null || date2 == null) {
            return false;
        }
        return date1.isBefore(date2) || date1.isEqual(date2);
    }

}
package com.jd.kf.oss.performance.utils;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Map;

/**
 * @author: songweijia1
 * @description: map获取值工具类
 * @date: 2025/7/7
 */

public class MapHelper {

    public static String getMapString(Map<String, String> curMap, String key) {
        return MapUtils.getString(curMap, key);
    }
//
//    public static Date getMapDate(Map<String, String> curMap, String key) {
//        String value = MapUtils.getString(curMap, key);
//        if (StringUtils.isBlank(value) || "null".equals(value)) {
//            return null;
//        }
//        try {
//            Date date = DateUtils.parseStringToDate(value, DateUtils.TIME_PATTERN);
//            return date;
//        }catch (Exception e){
//            return null;
//        }
//    }

    public static LocalDateTime getMapLocalDateTime(Map<String, String> curMap, String key) {
        String value = MapUtils.getString(curMap, key);
        if (StringUtils.isBlank(value) || "null".equals(value)) {
            return null;
        }
        try {
            LocalDateTime dateTime = DateUtils.stringToLocalDateTime(value, DateUtils.TIME_PATTERN);
            return dateTime;
        }catch (Exception e){
            return null;
        }
    }

//    public static Long getMapLong(Map<String, String> curMap, String key) {
//        String value = MapUtils.getString(curMap, key);
//        if (StringUtils.isBlank(value)|| "null".equals(value)) {
//            return null;
//        }
//        try {
//            return Long.parseLong(value);
//        }catch (Exception e){
//            return null;
//        }
//    }

    public static Integer getMapInt(Map<String, String> curMap, String key) {
        String value = MapUtils.getString(curMap, key);
        if (StringUtils.isBlank(value) || "null".equals(value)) {
            return null;
        }
        try {
            return Integer.parseInt(value);
        }catch (Exception e){
            return null;
        }
    }

    public static Boolean getMapBoolean(Map<String, String> curMap, String key) {
        String value = MapUtils.getString(curMap, key);
        if (StringUtils.isBlank(value) || "null".equals(value)) {
            return null;
        }
        try {
            return Boolean.parseBoolean(value);
        }catch (Exception e){
            return null;
        }
    }

}

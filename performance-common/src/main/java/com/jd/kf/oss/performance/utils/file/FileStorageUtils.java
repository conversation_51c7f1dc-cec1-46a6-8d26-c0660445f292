package com.jd.kf.oss.performance.utils.file;

import com.jd.jss.JingdongStorageService;
import com.jd.jss.domain.StorageObject;
import com.jd.jss.support.SignRules;
import com.jd.laf.config.spring.annotation.LafValue;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.net.URI;

/**
 * @author:zmf
 * @date:2021/9/17
 */
@Service
@Slf4j
public class FileStorageUtils {

    private static final String BUCKET_NAME = "im.roster";

    public static String property = System.getProperty("user.dir");
    public static String localDataDir =  "/performance/tempFiles/";

    @Autowired
    private JingdongStorageService ddFileJingdongStorageService;

    /**
     * 需要过滤的特殊字符
     */
    @LafValue("predict.filter.special.characters")
    private String filterSpecialCharacters = "[!@#$%^&*\\\\/]";

//    public InputStream getInputStream(String url) {
//        BucketFileNameCombine bucketAndFileName = getBucketAndFileName(url);
//        StorageObject storageObject = ddFileJingdongStorageService
//                .bucket(bucketAndFileName.getBucket())
//                .object(bucketAndFileName.getFileName())
//                .get();
//        return storageObject.getInputStream();
//    }


    public File getFile(String url) {
        try {
            BucketFileNameCombine bucketAndFileName = getBucketAndFileName(url);
            StorageObject storageObject = ddFileJingdongStorageService
                    .bucket(bucketAndFileName.getBucket())
                    .object(bucketAndFileName.getFileName())
                    .get();
            FileUtil.checkAndMakeDir(FileUtil.getLocalDataDir(property + localDataDir));
            String filePath = property + localDataDir +bucketAndFileName.getFileName();
            File file = new File(filePath);
            storageObject.toFile(file);
            return  file;
        }catch (Exception e){
          log.error("RosterFileStorageServiceImpl.getFile error ={}",e.getMessage());
        }
        return null;
    }
    /**
     * 获取默认的过期时间（当前时间加7天）
     * @return 默认的过期时间对象
     */
    private DateTime getDefaultExpireTime(){
        return DateTime.now().plusDays(7);
    }

    /**
     * 获取一个表示永不过期的时间点（当前时间+100年）
     * @return 返回100年后的DateTime时间对象
     */
    private DateTime getNeverExpireTime(){
        return DateTime.now().plusYears(100);
    }


    /**
     * 上传文件到指定位置
     * @param fileName 要上传的文件名称
     * @param outputStream 包含文件内容的字节数组输出流
     * @return 文件上传后的访问路径
     */
    public String upload(String fileName, ByteArrayOutputStream outputStream) {
        return uploadWithExpireTime(fileName, outputStream, getDefaultExpireTime());
    }

    /**
     * 上传文件到存储桶并生成永久有效的文件访问URL
     * @param fileName 原始文件名，方法内部会自动过滤特殊字符
     * @param outputStream 包含文件内容的字节数组输出流
     * @return 永久有效的文件访问URL
     */
    public String uploadWithoutExpireTime(String fileName, ByteArrayOutputStream outputStream) {
        checkAndCreateBucket();
        String processedFileName=fileName.replaceAll(filterSpecialCharacters,"");
        uploadFile(processedFileName, outputStream);
        return generateFileUrl(processedFileName,getNeverExpireTime());
    }
    /**
     * 上传文件并设置过期时间
     * @param fileName 文件名，需过滤特殊字符
     * @param outputStream 文件内容的字节数组输出流
     * @param timeout 文件过期时间
     * @return 生成的文件访问URL
     */
    public String uploadWithExpireTime(String fileName, ByteArrayOutputStream outputStream , DateTime timeout) {
        checkAndCreateBucket();
        String processedFileName=fileName.replaceAll(filterSpecialCharacters,"");
        uploadFile(processedFileName, outputStream);
        return generateFileUrl(processedFileName, timeout);
    }

    /**
     * 上传文件到京东云存储服务
     * @param fileName 要上传的文件名称
     * @param outputStream 包含文件内容的字节数组输出流
     */
    private void uploadFile(String fileName,ByteArrayOutputStream outputStream) {
        ddFileJingdongStorageService.bucket(BUCKET_NAME).object(fileName)
                .entity(outputStream.size(), new ByteArrayInputStream(outputStream.toByteArray()))
                .put();
    }

    /**
     * 生成带有效期的文件访问URL
     * @param fileName 文件名
     * @param timeout 过期时间
     * @return 带签名的文件访问URL字符串
     */
    private String generateFileUrl(String fileName,DateTime timeout){
        SignRules signRules = new SignRules(timeout);
        URI uri = ddFileJingdongStorageService.bucket(BUCKET_NAME)
                .object(fileName)
                .generatePresignedUrl(signRules);
        return uri.toString();
    }



    private void checkAndCreateBucket() {
        if (ddFileJingdongStorageService.hasBucket(BUCKET_NAME)) {
            return;
        }
        ddFileJingdongStorageService.createBucket(BUCKET_NAME);
    }

    private BucketFileNameCombine getBucketAndFileName(String url) {
        int questionMarPos = url.indexOf("?");
        int slashPos = url.lastIndexOf("/", questionMarPos);
        String fileName = url.substring(slashPos + 1, questionMarPos);
        int slashPos2 = url.lastIndexOf("/", slashPos - 1);
        return BucketFileNameCombine.builder()
                .bucket(url.substring(slashPos2 + 1, slashPos))
                .fileName(fileName.replaceAll(filterSpecialCharacters,""))
                .build();
    }


    @Builder
    @Getter
    private static class BucketFileNameCombine {

        private final String bucket;

        private final String fileName;
    }



}
